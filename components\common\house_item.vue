<template>
    <view class="house_item" @click="$Router.push(`/pages/made/house_detail/house_detail?id=${info.id}`)">
        <view class="house_card">
            <!-- 房源图片 -->
            <view class="house_image_container">
                <image class="house_image" :src="$t.getImgUrl(info.piclink)" mode="aspectFill">
                </image>
            </view>

            <!-- 房源信息 -->
            <view class="house_info">
                <!-- 标题 -->
                <view class="house_title">{{ info.title || "---" }}</view>

                <!-- 房型信息 -->
                <view class="house_details">
                    <text class="detail_text">{{ info.rooms || '3室2厅' }}</text>
                    <text class="detail_separator">|</text>
                    <text class="detail_text">{{ info.area || '90' }}㎡</text>
                    <text class="detail_separator">|</text>
                    <text class="detail_text">{{ info.orientation || '朝南' }}</text>
                    <text class="detail_separator">|</text>
                    <text class="detail_text">{{ info.district || '晋安府三' }}</text>
                </view>

                <!-- 标签 -->
                <view class="house_tags">
                    <view class="house_tag youzhi">
                        优质户型
                    </view>
                    <view class="house_tag" v-for="(tag, index) in info.tags" :key="index">
                        {{ tag }}
                    </view>
                </view>
                <view class="house_price">
                    <view>{{ $u.priceFormat(info.price, 0) }} <text>万</text></view>
                    <view>{{ $u.priceFormat(info.pricePerSqm, 0) }}元/㎡</view>
                </view>

            </view>
        </view>
        <!-- 价格和操作区域 -->
        <template v-if="isBottom">
            <view class="house_footer" v-if="type == 0">
                <!-- 左侧：状态和价格 -->
                <view class="status_container">
                    {{ info.status }}
                </view>

                <view class="action_buttons">
                    <view class="action_btn secondary_btn"
                        @click.stop="$Router.push(`/pages/made/publish_house/publish_house?id=${info.id}`)">
                        <text class="action_text">重新编辑</text>
                    </view>
                    <view class="action_btn primary_btn" @click.stop="handlePrimaryAction">
                        <text class="action_text">立即下架</text>
                    </view>
                </view>
            </view>
            <view class="house_footer" v-if="type == 1">

                <view class="action_buttons">
                    <view class="action_btn secondary_btn" @click.stop="">
                        <text class="action_text">取消收藏</text>
                    </view>

                </view>
            </view>
        </template>

    </view>
</template>

<script>
export default {
    name: 'HouseItem',
    props: {
        info: {
            type: Object,
            default: () => ({
                id: '',
                image: '',
                title: '',
                rooms: '',
                area: '',
                orientation: '',
                district: '',
                price: '',
                pricePerSqm: '',
                status: '', // 待审核、已发布、已驳回
                tags: []
            })
        },
        isBottom: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: "0" // 0历史房源 1我的收藏
        }
    },
    methods: {
        getStatusClass() {
            const status = this.info.status;
            switch (status) {
                case '待审核':
                    return 'status_pending';
                case '已发布':
                    return 'status_published';
                case '已驳回':
                    return 'status_rejected';
                default:
                    return 'status_published';
            }
        },

        getStatusText() {
            return this.info.status || '已发布';
        },

        getSecondaryButtonText() {
            const status = this.info.status;
            switch (status) {
                case '已发布':
                    return '重新编辑';
                case '已驳回':
                    return '重新编辑';
                default:
                    return '重新编辑';
            }
        },

        getPrimaryButtonText() {
            const status = this.info.status;
            switch (status) {
                case '已发布':
                    return '立即下架';
                case '已驳回':
                    return '重新发布';
                default:
                    return '立即下架';
            }
        },

        handleSecondaryAction() {
            this.$emit('secondary-action', {
                action: this.getSecondaryButtonText(),
                data: this.info
            });
        },

        handlePrimaryAction() {
            uni.showModal({
                title: '提示',
                content: '是否确定下架此房源？',
                success: function (res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.house_item {
    width: 100%;
    padding: 25rpx;
    margin-bottom: 20rpx;
    background: #FFFFFF;
    border-radius: 16rpx;

    .house_card {
        width: 100%;
        @include flex-center(row, flex-start, flex-start);
        gap: 20rpx;

        .house_image_container {
            width: 185rpx;
            height: 222rpx;
            border-radius: 12rpx;
            overflow: hidden;

            .house_image {
                width: 100%;
                height: 100%;
            }
        }

        .house_info {
            flex: 1;
            @include flex-center(column, null, null);
            gap: 10rpx;

            >view {
                width: 100%;
            }

            .house_title {
                font-weight: bold;
                font-size: 26rpx;
                color: #000000;
                line-height: 36rpx;
                @include text-overflow(100%, 2);
            }

            .house_details {
                @include flex-center(row, flex-start, center);

                .detail_text {
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #333333;
                }

                .detail_separator {
                    font-size: 26rpx;
                    color: #CCCCCC;
                    margin: 0 12rpx;
                }
            }

            .house_tags {
                @include flex-center(row, flex-start, center, wrap);
                gap: 10rpx;

                .house_tag {
                    padding: 4rpx 8rpx;
                    background: #F5F5F5;
                    border-radius: 4rpx;
                    font-weight: 500;
                    font-size: 22rpx;
                    color: #8A8A8A;

                    &.youzhi {
                        background: #F9F3EC;
                        color: #B28C62;
                    }
                }
            }

            .house_price {
                @include flex-center(row, flex-start, flex-end);
                gap: 15rpx;


                >view:nth-child(1) {
                    font-family: HarmonyOS Sans TC, HarmonyOS Sans TC;
                    font-weight: bold;
                    font-size: 34rpx;
                    color: #E62222;

                    >text {
                        font-size: 24rpx;

                    }
                }

                >view:nth-child(2) {
                    font-weight: 500;
                    font-size: 20rpx;
                    color: #999999;
                    line-height: 34rpx;
                }
            }

        }
    }

    .house_footer {
        @include flex-center(row, space-between, center);
        width: 100%;
        margin-top: 25rpx;



        .status_container {
            font-weight: 500;
            font-size: 24rpx;
            color: #EE1616;
            flex-shrink: 0;

            &.status_pending {
                color: #FF6B35;
            }

            &.status_published {
                color: #F63030;
            }

            &.status_rejected {
                color: #999999;
            }
        }



        .action_buttons {
            flex: 1;
            @include flex-center(row, flex-end, center, wrap);
            gap: 15rpx;

            .action_btn {
                padding: 10rpx 24rpx;
                border-radius: 50rpx;
                font-size: 26rpx;

                &.secondary_btn {
                    border: 1rpx solid #A2A2A2;
                    font-weight: 500;
                    color: #A2A2A2;
                }

                &.primary_btn {
                    background: #006AFC;

                    .action_text {
                        color: #FFFFFF;
                    }
                }
            }
        }
    }
}
</style>