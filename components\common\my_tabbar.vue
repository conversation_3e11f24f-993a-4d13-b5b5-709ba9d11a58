<template>
	<view class="my_tabbar">
		<view class="tabbar_item" :class="{ 'active': currentIndex === index, 'move': index == 2 }"
			v-for="(item, index) in tabbarList" :key="index" @click="tabbarItemClick(index)">
			<view class="tabbar_item_icon">
				<image :src="currentIndex === index ? item.activeIcon : item.icon" mode="aspectFill"></image>
			</view>
			<view class="tabbar_item_title" :style="{ color: currentIndex === index ? baseColor : '' }">
				{{ item.title }}
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		currentIndex: {
			type: Number || String,
			default: 0
		}
	},
	data() {
		return {
			tabbarList: [{
				title: '首页',
				path: '/pages/home/<USER>/dz_index',
				icon: require('@/static/page/footer_icon1_1.png'),
				activeIcon: require('@/static/page/footer_icon1_2.png'),
			}, {
				title: '律所',
				path: '/pages/home/<USER>/law_firm',
				icon: require('@/static/page/footer_icon2_1.png'),
				activeIcon: require('@/static/page/footer_icon2_2.png'),
			}, {
				title: '',
				path: '/pages/made/publish_house/publish_house',
				icon: require('@/static/page/footer_icon3_1.png'),
				activeIcon: require('@/static/page/footer_icon3_1.png'),
			}, {
				title: '订单',
				path: '/pages/home/<USER>/orderlist',
				icon: require('@/static/page/footer_icon5_1.png'),
				activeIcon: require('@/static/page/footer_icon5_2.png'),
			}, {
				title: '我的',
				path: '/pages/home/<USER>/userinfo',
				icon: require('@/static/page/footer_icon4_1.png'),
				activeIcon: require('@/static/page/footer_icon4_2.png'),
			}]
		}
	},
	mounted() { },
	methods: {
		tabbarItemClick(index) {

			if (this.currentIndex !== index) {
				console.log(this.tabbarList[index]);
				this.$t.gotoLink(this.tabbarList[index].path)
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.my_tabbar {
	width: 100%;
	height: calc(100rpx + env(safe-area-inset-bottom));
	position: fixed;
	bottom: 0;
	left: 0;
	background-color: #fff;
	z-index: 9999;
	@include flex-center(row, space-around, center, nowrap);
	padding-bottom: constant(100rpx + safe-area-inset-bottom);
	padding-bottom: env(100rpx + safe-area-inset-bottom);
	border-radius: 54rpx 54rpx 0rpx 0rpx;

	.tabbar_item {
		@include flex-center(column, center, center, nowrap);
		gap: 5rpx;

		.tabbar_item_icon {
			width: 44rpx;
			height: 44rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.tabbar_item_title {
			font-size: 24rpx;
			color: #999;
		}



		&.move {
			transform: translateY(-30rpx);

			.tabbar_item_icon {
				width: 140rpx;
				height: 127rpx;
			}
		}
	}
}
</style>