<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList"
			bgColor="url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_bg.png') no-repeat center top/100% 320rpx #f7f7f7 "
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="" mpWeiXinShow :autoBack="true" bgColor="transparent" :fixed="false"
					class="custom_navbar">
					<view class="ordersearch" slot="center">
						<u-search placeholder="请输入" bgColor="#fff" :showAction="false" v-model="title"
							@search=""></u-search>
					</view>
				</cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="search_top">
					<view class="search_top_header">
						<view>龙湖兰园天序</view>
						<view>
							查看小区详情
							<u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
						</view>
					</view>
					<view class="search_top_main">
						<view class="search_top_main_left">
							<image :src="$t.getImgUrl()" mode="scaleToFill" />
						</view>
						<view class="search_top_main_right">
							<view>
								<view>住宅</view>
								|
								<view>仓山区</view>
								-
								<view>金山公园</view>
							</view>
							<view>
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/fire_icon.png"
									mode="scaleToFill" />
								推荐理由:
								<view>比同小区同户型均价便宜30%</view>
							</view>
							<view>
								22098<text>元/平</text>
							</view>
						</view>
					</view>
				</view>
				<view class="loading_container">
					<searchList></searchList>
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>

	</view>
</template>

<script>
import searchList from '@/components/common/search_list.vue';

export default {
	components: {
		searchList
	},
	data() {
		return {
			refresherStatus: 0,
			title: "",
			dataList: [],
		};
	},
	computed: {

	},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},

	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.ordersearch {
		width: 100%;
		padding: 0 20rpx 0 80rpx;
		border-radius: 70rpx;
	}


	.loading_list {
		width: 100%;
		height: 100%;



		.search_top {
			padding: 20rpx 25rpx;

			.search_top_header {
				@include flex-center(row, space-between, center);

				>view:nth-child(1) {
					flex: 1;
					@include text_overflow(100%, 1);
					font-weight: bold;
					font-size: 36rpx;
					color: #000000;
				}

				>view:nth-child(2) {
					font-weight: 500;
					font-size: 26rpx;
					color: #777777;
					@include flex-center(row, center, center);
					gap: 5rpx;
				}
			}

			.search_top_main {
				@include flex-center(row, space-between, center);
				gap: 25rpx;
				margin-top: 25rpx;

				.search_top_main_left {
					width: 174rpx;
					height: 174rpx;
					border-radius: 12rpx;
					overflow: hidden;

					>image {
						width: 100%;
						height: 100%;
					}
				}

				.search_top_main_right {
					height: 174rpx;
					@include flex-center(column, space-between, null);
					flex: 1;

					>view:nth-child(1) {
						@include flex-center(row, flex-start, center);
						gap: 5rpx;
						font-weight: bold;
						font-size: 30rpx;
						color: #000000;

						>view:nth-child(3) {
							@include text_overflow(100%, 1);
							flex: 1;
						}
					}

					>view:nth-child(2) {
						@include flex-center(row, flex-start, center);
						gap: 8rpx;
						background: linear-gradient(90deg, #FBF5E7 0%, rgba(251, 245, 231, 0) 100%);
						border-radius: 4rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #784720;

						>image {
							width: 30rpx;
							height: 30rpx;
						}

						>view {
							flex: 1;

							@include text_overflow(100%, 1);

						}
					}

					>view:nth-child(3) {
						font-weight: bold;
						font-size: 34rpx;
						color: #E62222;

						>text {
							font-size: 24rpx;

						}
					}
				}
			}
		}

		.loading_container {
			flex: 1;
			padding: 25rpx;
			background: #FFFFFF;
			border-radius: 30rpx 30rpx 0rpx 0rpx;


			>view {
				width: 100%;
			}
		}


	}
}
</style>