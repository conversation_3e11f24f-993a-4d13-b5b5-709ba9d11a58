<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#fff"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="发布房源" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<image class="status_img" src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/sucess_status.png"
					mode=""></image>
				<view class="status_title">
					提交成功
				</view>
				<view class="status_text">
					请等待平台审核，可随时关注发布进度
				</view>
				<view class="status_btn" @click="$Router.replaceAll(`/pages/made/properties_list/properties_list`)">
					查看详情
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		padding: 35rpx 25rpx 0 25rpx;
		@include flex_center(column, center, center);

		.status_img {
			width: 150rpx;
			height: 150rpx;
		}

		.status_title {
			font-weight: bold;
			font-size: 32rpx;
			color: #000000;
			line-height: 50rpx;
			margin-top: 20rpx;
		}

		.status_text {
			font-weight: 500;
			font-size: 26rpx;
			color: #B2B2B2;
			line-height: 50rpx;
			margin-top: 15rpx;
		}

		.status_btn {
			@include flex_center(row, center, center);
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			width: 400rpx;
			height: 88rpx;
			background: #006AFC;
			border-radius: 44rpx;
			margin-top: 160rpx;
		}
	}
}
</style>