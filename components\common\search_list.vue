<template>
    <view>
        <u-sticky bgColor="#fff" offsetTop="0" customNavHeight="0">
            <!-- 推荐标签区域 -->
            <view class="recommend_tags">
                <u-tabs :list="tabOption" lineWidth="32rpx" lineHeight="9rpx" :lineColor="`url(${lineBg}) 100% 100%`"
                    :activeStyle="activeStyle" :inactiveStyle="inactiveStyle" itemStyle="height: 36px;"
                    @click="tabClick"></u-tabs>
            </view>

            <!-- 筛选按钮区域 -->
            <view class="filter_buttons">
                <view class="filter_button" @click="showPriceSelector">
                    <text class="filter_text" :class="{ active: form.price.value !== '' }">
                        {{ form.price.value || '售价' }}</text>
                    <u-icon name="arrow-down-fill" size="20rpx"
                        :color="form.price.value !== '' ? '#006AFC' : '#333333'"></u-icon>
                </view>
                <view class="filter_button" @click="showRoomSelector">
                    <text class="filter_text" :class="{ active: form.room.value !== '' }">
                        {{ form.room.label || '户型' }}</text>
                    <u-icon name="arrow-down-fill" size="20rpx"
                        :color="form.room.value !== '' ? '#006AFC' : '#333333'"></u-icon>
                </view>
                <view class="filter_button" @click="showAreaSelector">
                    <text class="filter_text" :class="{ active: form.area.value !== '' }">
                        {{ form.area.label || '面积' }}</text>
                    <u-icon name="arrow-down-fill" size="20rpx"
                        :color="form.area.value !== '' ? '#006AFC' : '#333333'"></u-icon>
                </view>
                <view class="filter_button" @click="showSortSelector">
                    <text class="filter_text" :class="{ active: form.sort.value !== '' }">
                        {{ form.sort.label || '排序' }}</text>
                    <u-icon name="arrow-down-fill" size="20rpx"
                        :color="form.sort.value !== '' ? '#006AFC' : '#333333'"></u-icon>
                </view>
            </view>

            <!-- 筛选标签区域 -->
            <view class="filter_tags">
                <view class="filter_tag" :class="{ active: tagCurrent == index }" v-for="(item, index) in filterTags"
                    :key="index" @click="tagClick(index, '.filter_tag')">
                    {{ item }}
                </view>
            </view>
        </u-sticky>

        <!-- 房源列表 -->
        <view class="house_list">
            <house-item v-for="(item, index) in 10" :key="index" :info="{}"></house-item>
        </view>

        <!-- 售价选择器 -->
        <u-picker :show="showPrice" :columns="priceColumns" @confirm="confirmPrice" @cancel="showPrice = false"
            keyName="label" title="选择售价范围"></u-picker>

        <!-- 户型选择器 -->
        <u-picker :show="showRoom" :columns="roomColumns" @confirm="confirmRoom" @cancel="showRoom = false"
            keyName="label" title="选择户型"></u-picker>

        <!-- 面积选择器 -->
        <u-picker :show="showArea" :columns="areaColumns" @confirm="confirmArea" @cancel="showArea = false"
            keyName="label" title="选择面积范围"></u-picker>

        <!-- 排序选择器 -->
        <u-picker :show="showSort" :columns="sortColumns" @confirm="confirmSort" @cancel="showSort = false"
            keyName="label" title="选择排序方式"></u-picker>
    </view>
</template>
<script>
import HouseItem from '@/components/common/house_item.vue';

export default {
    components: {
        HouseItem
    },
    data() {
        return {
            lineBg: "https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/tabline.png",
            activeStyle: {
                'font-weight': 'bold',
                'font-size': '32rpx',
                'color': '#000000',
            },
            inactiveStyle: {
                'font-weight': 'bold',
                'font-size': '30rpx',
                'color': ' #777777',
            },
            title: "",
            filterTags: ['全部', '近地铁', '有学区', '全明格局', '大客厅', '有车位'],
            tagCurrent: 0,

            // 选择器显示状态
            showPrice: false,
            showRoom: false,
            showArea: false,
            showSort: false,

            // 筛选表单数据
            form: {
                tab: 0,
                price: { label: '', value: '' },
                room: { label: '', value: '' },
                area: { label: '', value: '' },
                sort: { label: '', value: '' }
            },

            // 选择器数据
            priceColumns: [
                [
                    { label: '不限', value: '' },
                    { label: '100万以下', value: '0-100' },
                    { label: '100-150万', value: '100-150' },
                    { label: '150-200万', value: '150-200' },
                    { label: '200-300万', value: '200-300' },
                    { label: '300-500万', value: '300-500' },
                    { label: '500万以上', value: '500-' }
                ]
            ],
            roomColumns: [
                [
                    { label: '不限', value: '' },
                    { label: '1室', value: '1' },
                    { label: '2室', value: '2' },
                    { label: '3室', value: '3' },
                    { label: '4室', value: '4' },
                    { label: '5室及以上', value: '5+' }
                ]
            ],
            areaColumns: [
                [
                    { label: '不限', value: '' },
                    { label: '50㎡以下', value: '0-50' },
                    { label: '50-70㎡', value: '50-70' },
                    { label: '70-90㎡', value: '70-90' },
                    { label: '90-120㎡', value: '90-120' },
                    { label: '120-150㎡', value: '120-150' },
                    { label: '150㎡以上', value: '150-' }
                ]
            ],
            sortColumns: [
                [
                    { label: '默认排序', value: 'default' },
                    { label: '价格从低到高', value: 'price_asc' },
                    { label: '价格从高到低', value: 'price_desc' },
                    { label: '面积从小到大', value: 'area_asc' },
                    { label: '面积从大到小', value: 'area_desc' },
                    { label: '最新发布', value: 'time_desc' }
                ]
            ],

        };
    },
    computed: {
        tabOption() {
            let refresherStatus = this.refresherStatus
            return [
                { name: '推荐', val: '' },
                { name: '优质', val: '优质' },
                { name: '急售', val: '急售' },


            ]
        }
    },
    onLoad() { },
    methods: {
        // 显示选择器方法
        showPriceSelector() {
            this.showPrice = true;
        },
        showRoomSelector() {
            this.showRoom = true;
        },
        showAreaSelector() {
            this.showArea = true;
        },
        showSortSelector() {
            this.showSort = true;
        },

        // 确认选择方法
        confirmPrice(e) {
            this.form.price = e.value[0];
            this.showPrice = false;
        },
        confirmRoom(e) {
            this.form.room = e.value[0];
            this.showRoom = false;
        },
        confirmArea(e) {
            this.form.area = e.value[0];
            this.showArea = false;
        },
        confirmSort(e) {
            this.form.sort = e.value[0];
            this.showSort = false;
        },

        tabMove(index, className) {
            this.$nextTick(() => {
                // 获取所有标签元素
                const tabItems = document.querySelectorAll(className);
                if (tabItems && tabItems.length > 0) {
                    // 获取被点击的标签元素
                    const activeTab = tabItems[index];
                    // 获取标签容器
                    const tabContainer = activeTab.parentElement;
                    if (activeTab && tabContainer) {
                        // 计算滚动位置：标签的左边距 + 标签宽度的一半 - 容器宽度的一半
                        const scrollLeft = activeTab.offsetLeft + (activeTab.offsetWidth / 2) - (tabContainer.offsetWidth / 2);
                        // 平滑滚动到计算出的位置
                        tabContainer.scrollTo({
                            left: scrollLeft,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        },
        tagClick(index, className) {
            this.tagCurrent = index;
            this.tabMove(index, className);
        },
    },
};
</script>
<style lang="scss" scoped>
// 推荐标签区域
.recommend_tags {
    @include flex-center(row, flex-start, center);
    gap: 35rpx;

    .recommend_tag {
        font-weight: bold;
        font-size: 32rpx;
        color: #777777;

        &.active {
            color: #000000;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                bottom: -10rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 32rpx;
                height: 8rpx;
                background: #006AFC;
                border-radius: 4rpx;
            }
        }
    }
}

// 筛选按钮区域
.filter_buttons {
    @include flex-center(row, space-around, center);
    overflow-x: auto;
    scroll-behavior: smooth;
    margin-top: 20rpx;

    .filter_button {
        @include flex-center(row, center, center);
        gap: 8rpx;
        cursor: pointer;
        transition: all 0.3s ease;

        .filter_text {
            font-weight: 500;
            font-size: 26rpx;
            color: #333333;
            transition: color 0.3s ease;

            &.active {
                color: #006AFC;
                font-weight: bold;
            }
        }
    }
}

// 筛选标签区域
.filter_tags {
    @include flex-center(row, flex-start, center);
    margin-top: 20rpx;
    gap: 15rpx;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding-bottom: 30rpx;

    .filter_tag {
        flex-shrink: 0;
        padding: 8rpx 25rpx;
        border-radius: 100rpx;
        background: #F8F8F8;
        font-weight: 500;
        font-size: 24rpx;
        color: #707070;
        transition: all 0.3s ease;

        &.active {
            background: #006AFC;
            color: #FFFFFF;
        }
    }
}

// 房源列表
.house_list {
    /deep/.house_item {
        margin-bottom: 0;
        padding: 25rpx 0;
        border-bottom: 1rpx solid #eee;
        border-radius: 0;

        &:last-child {
            border-bottom: 0;
        }
    }
}
</style>