<template>
	<view class="userinfo">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#F5F5F6">
			<view slot="top">
			</view>
			<view class="userinfo_body"
				:style="{ 'padding-top': ($u.getPx('25rpx') + navHeight) + 'px', 'background': 'url(' + (userInfo.piclink ? $t.getImgUrl(userInfo.piclink) : 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/user_bj2.png') + ')  no-repeat center top/100% auto' }">
				<view v-if="isAppletPlatform && userInfo.is_applets_login == 0"></view>
				<view class="userinfo_top_name_info_top_setting" v-else>
					<view v-if="flagInfo.top && flagInfo.top.data.sms" @click="$t.gotoLink('/pages/user/notice/notice')"
						style="position:relative">
						<u-icon name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/top_3.png" size="38rpx"
							color="#000"></u-icon>
						<u-badge type="error" max="99" :value="userInfo.is_view" :absolute="true"
							:offset="[-10, 0]"></u-badge>
					</view>

					<view class=""
						v-if="flagInfo.top && flagInfo.top.data && flagInfo.top.data.sz && (!isAppletPlatform || (isAppletPlatform && userInfo.is_applets_login == 1))">
						<u-icon name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/top_1.png" size="38rpx"
							color="#000" @click="$Router.push('/pages/setting/index/index')"></u-icon>
					</view>
				</view>
				<view class="userinfo_top_name userinfo_padding"
					v-if="isAppletPlatform && userInfo.is_applets_login == 0">
					<view class="userinfo_top_name_avatar" @click="openAppletLogin">
						<image :src="$t.getImgUrl($configInfo.maleDefaultAvatar)" mode="aspectFill"></image>
					</view>
					<view class="userinfo_top_name_info" @click="openAppletLogin">
						<text class="top_name_nickname" style="font-size: 32rpx;">点击前往登录</text>
						<appletLogin ref="appletLogin" @close="closeAppletLogin"></appletLogin>
					</view>
				</view>

				<view class="userinfo_top_name userinfo_padding" v-else>
					<view class="userinfo_top_name_avatar">
						<image :src="$t.getImgUrl(userInfo.avatar)" mode="aspectFill"></image>
						<!-- <text> {{ userInfo.rating == 1 && userInfo.coin_rating > 1 ? userInfo.coin_rating_cn :
							userInfo.rating_cn }}</text> -->
					</view>
					<view class="userinfo_top_name_info">
						<view class="userinfo_top_name_info_top">
							<text class="top_name_nickname" v-if="userInfo.nickname">{{ userInfo.nickname }}</text>
							<view class="top_name_editname" v-else @click="form.nickname = ''; nicknameShow = true;">
								<text>暂未设置昵称</text>
								<u-icon name="edit-pen" color="#000" size="30rpx"></u-icon>

								<u-popup :show="nicknameShow" :customStyle="{ 'borderRadius': '10rpx' }"
									bgColor="transparent" mode="center" @close="nicknameShow = false">
									<view class="nicknamepop">
										<view class="pop_title"> 编辑昵称 </view>
										<u--input customStyle="width:94%" placeholder="请输入昵称" border="surround"
											v-model="form.nickname"></u--input>
										<view class="pop_btn">
											<view @click="nicknameShow = false">取消</view>
											<view @click="confirmNickname">确认</view>
										</view>
									</view>
								</u-popup>
							</view>

						</view>

						<view class="userinfo_top_name_bottom">
							<view class="top_name_invite" @click="copyUserName">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/top_name_invite.png">
								</image>
								<text>邀请码：{{ userInfo.username }}</text>
							</view>
							<view class="top_name_invite"
								v-if="flagInfo.top && flagInfo.top.data.tjr && userInfo.tid_cn">
								<text v-if="userInfo.tid_cn.nickname && userInfo.tid_cn.nickname.length > 5">推荐人：{{
									userInfo.tid_cn.nickname.slice(0, 5) }} ...</text>
								<text v-else>推荐人：{{ userInfo.tid_cn.nickname || userInfo.tid_cn.username }}</text>
							</view>
						</view>

					</view>
					<view class="userinfo_top_name_btn" @click="gotoShare">
						邀请好友
						<u-icon name="play-right-fill" color="#fff" size="20rpx"></u-icon>
					</view>
				</view>
				<view class="userinfo_box">
					<view class="userinfo_box_item" @click="$Router.push('/pages/made/publish_house/publish_house')">
						<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_top_1.png"
							mode="scaleToFill" />
						发布房源
					</view>
					<view class="userinfo_box_item" @click="$Router.push('/pages/made/appraise/appraise')">
						<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_top_2.png"
							mode="scaleToFill" />
						房屋估价
					</view>
					<view class="userinfo_box_item"
						@click="$Router.push('/pages/made/properties_list/properties_list?type=0')">
						<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_top_3.png"
							mode="scaleToFill" />
						历史房源
					</view>
					<view class="userinfo_box_item"
						@click="$Router.push('/pages/made/properties_list/properties_list?type=1')">
						<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_top_4.png"
							mode="scaleToFill" />
						我的收藏
					</view>
				</view>
				<!-- 我的委托 -->
				<view class="userinfo_padding userinfo_margintop">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">我的委托</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item"
								@click="$Router.push('/pages/order/orderlist/orderlist?status=未支付')">
								<view class="tool_item_image">
									<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_1.png"
										mode="aspectFit"></image>
									<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
										:offset="[-2, -5]" :value="userInfo.my_order_num_1"></u-badge>
								</view>
								<text>待付款</text>
							</view>
							<view class="tool_item"
								@click="$Router.push('/pages/order/orderlist/orderlist?status=已支付')">
								<view class="tool_item_image">
									<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_2.png"
										mode="aspectFit"></image>
									<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
										:offset="[-2, -5]" :value="userInfo.my_order_num_2"></u-badge>
								</view>
								<text>待分配</text>
							</view>
							<view class="tool_item"
								@click="$Router.push('/pages/order/orderlist/orderlist?status=已发货')">
								<view class="tool_item_image">
									<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_3.png"
										mode="aspectFit"></image>
									<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
										:offset="[-2, -5]" :value="userInfo.my_order_num_4"></u-badge>
								</view>
								<text>已完成</text>
							</view>
							<view class="tool_item"
								@click="$Router.push('/pages/order/orderlist/orderlist?status=待评价')">
								<view class="tool_item_image">
									<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_4.png"
										mode="aspectFit"></image>
									<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
										:offset="[-2, -5]" :value="userInfo.my_order_num_1"></u-badge>
								</view>
								<text>已完成</text>
							</view>
						</view>
					</view>
				</view>
				<!-- 我的权益 -->
				<view class="userinfo_padding userinfo_margintop">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">我的权益</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push('/pages/pay/cashflow/cashflow')">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_1.png"
									mode="aspectFit"></image>
								<text>我的钱包</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/news/protocol/protocol?id=5207')">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_2.png"
									mode="aspectFit"></image>
								<text>关于我们</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/user/footprint/footprint')">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_3.png"
									mode="aspectFit"></image>
								<text>我的足迹</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/user/feedback/feedback')">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_4.png"
									mode="aspectFit"></image>
								<text>投诉建议</text>
							</view>
							<view class="tool_item" v-if="userInfo.is_supplier == 0"
								@click="$Router.push('/pages/supplier/apply/apply')">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_5.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.shsz || "申请律所" }}</text>
							</view>

							<view class="tool_item" @click="$Router.push('/pages/supplier/platform/platform')">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_5.png"
									mode="aspectFit"></image>
								<text>律所中心</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/made/real_name/real_name')">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_6.png"
									mode="aspectFit"></image>
								<text>实名认证</text>
							</view>
							<view class="tool_item"
								@click="$Router.push(`/pages/chat/customer_service/customer_service`)">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_icon_7.png"
									mode="aspectFit"></image>
								<text>联系客服</text>
							</view>
						</view>
					</view>
				</view>
				<!-- <scroll-view class="userinfo_top_money" scroll-x="true" style="white-space: nowrap"
					v-if="moneyList && moneyList.length > 0">
					<view class="money_box">
						<view class="money_item yknumber"
							:class="moneyList.length == 2 ? 'money_item_2' : moneyList.length == 3 ? 'money_item_3' : ''"
							v-for="(it, i) in moneyList" :key="i"
							@click="$Router.push('/pages/pay/cashflow/cashflow?iden=' + it.iden)">
							<view class="money_item_tip"
								v-if="flagInfo.m_tx && flagInfo.m_tx.data && flagInfo.m_tx.data.cz && it.iden == 'money'">
								可充值 </view>
							<view class="money_item_tip"
								v-if="flagInfo.m_tx && flagInfo.m_tx.data && flagInfo.m_tx.data.tx && it.iden == 'amount'">
								可提现 </view>
							<text>{{ $u.priceFormat(userInfo[it.iden], it.iden == "integral" ? 0 : 2) }}</text>
							<text>{{ it.title }}</text>
						</view>
					</view>
				</scroll-view> -->
				<view class="userinfo_padding">
					<view class="userinfo_top_order">
						<view class="userinfo_top_share" v-if="flagInfo.top && flagInfo.top.data.hym"
							@click="gotoShare">
							<view class="">
								<text>会员权益</text>
								<text>{{ flagInfo.top.title1 }}</text>
							</view>
							<view class="">
								推广海报
							</view>
						</view>
						<view class="order_box"
							v-if="flagInfo.my_order.data.dfk || flagInfo.my_order.data.dfh || flagInfo.my_order.data.dsh || flagInfo.my_order.data.dpj || flagInfo.my_order.data.sh">
							<scroll-view class="order_scrool" scroll-x="true" style="white-space: nowrap">
								<view class="order_scrool_box">
									<view class="order_item"
										@click="$Router.push('/pages/order/orderlist/orderlist?status=未支付')"
										v-if="flagInfo.my_order.data.dfk">
										<view class="order_item_icon">
											<image
												src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/order_1.png"
												mode="aspectFit"></image>
											<u-badge numberType="overflow" type="error" max="99" color="#fff"
												:absolute="true" :offset="[-2, -5]"
												:value="userInfo.my_order_num_1"></u-badge>
										</view>
										<text>{{ flagInfo.my_order.data.dfk }}</text>
									</view>
									<view class="order_item"
										@click="$Router.push('/pages/order/orderlist/orderlist?status=已支付')"
										v-if="flagInfo.my_order.data.dfh">
										<view class="order_item_icon">
											<image
												src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/order_2.png"
												mode="aspectFit"></image>
											<u-badge numberType="overflow" type="error" max="99" color="#fff"
												:absolute="true" :offset="[-2, -5]"
												:value="userInfo.my_order_num_2"></u-badge>
										</view>

										<text>{{ flagInfo.my_order.data.dfh }}</text>
									</view>
									<view class="order_item"
										@click="$Router.push('/pages/order/orderlist/orderlist?status=已发货')"
										v-if="flagInfo.my_order.data.dsh">
										<view class="order_item_icon">
											<image
												src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/order_3.png"
												mode="aspectFit"></image>
											<u-badge numberType="overflow" type="error" max="99" color="#fff"
												:absolute="true" :offset="[-2, -5]"
												:value="userInfo.my_order_num_3"></u-badge>
										</view>

										<text>{{ flagInfo.my_order.data.dsh }}</text>
									</view>
									<view class="order_item"
										@click="$Router.push('/pages/order/orderlist/orderlist?status=待评价')"
										v-if="flagInfo.my_order.data.dpj">
										<view class="order_item_icon">
											<image
												src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/order_4.png"
												mode="aspectFit"></image>
											<u-badge numberType="overflow" type="error" max="99" color="#fff"
												:absolute="true" :offset="[-2, -5]"
												:value="userInfo.my_order_num_4"></u-badge>
										</view>

										<text>{{ flagInfo.my_order.data.dpj }}</text>
									</view>
									<view class="order_item"
										@click="$Router.push('/pages/order/orderlist/orderlist?status=已退单')"
										v-if="flagInfo.my_order.data.sh">
										<view class="order_item_icon">
											<image
												src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/order_5.png"
												mode="aspectFit"></image>
											<u-badge numberType="overflow" type="error" max="99" color="#fff"
												:absolute="true" :offset="[-2, -5]"
												:value="userInfo.my_order_num_5"></u-badge>
										</view>
										<text>{{ flagInfo.my_order.data.sh }}</text>
									</view>
									<view class="order_item"
										@click="$Router.push('/pages/order/errand_order/errand_order')"
										v-if="flagInfo.my_order.data.run">
										<view class="order_item_icon">
											<image
												src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/order_6.png"
												mode="aspectFill"></image>
										</view>

										<text>{{ flagInfo.my_order.data.run || '跑腿订单' }}</text>
									</view>
								</view>
							</scroll-view>
							<view class="order_item order_item_all"
								@click="$Router.push('/pages/order/orderlist/orderlist')">
								<view class="order_item_icon">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/order_7.png"
										mode="aspectFit"></image>
								</view>
								<text>全部订单</text>
							</view>
						</view>
						<view class="order_list_swiper " v-if="orderList && orderList.length > 0">
							<swiper vertical="true" :autoplay="true" :interval="8000" style="height: 158rpx;">
								<swiper-item v-for="(item, index) in orderList" :key="index" :circular="true">
									<view class="swiper_order_item">
										<view class="swiper_order_item_piclink">
											<image :src="$t.getImgUrl(item.p_piclink)" mode="aspectFill"></image>
											<text v-if="item.number > 1">共{{ item.number }}件</text>
										</view>
										<view class="swiper_order_item_content">
											<view class="">
												<text>{{ item.status }}</text>
												<text>{{ item.time }}</text>
											</view>
											<view class="">
												{{ item.p_title }}
											</view>
										</view>
										<view class="swiper_order_item_btn" v-if="item.status == '未支付'"
											@click="$Router.push(`/pages/pay/payment/payment?id=${item.id}`)">
											去支付
										</view>
										<view class="swiper_order_item_btn" v-else
											@click="$Router.push(`/pages/order/orderdetail/orderdetail?id=${item.id}`)">
											查看
										</view>
									</view>
								</swiper-item>
							</swiper>
						</view>
					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.tool && (flagInfo.tool.data.scsp || flagInfo.tool.data.foot)">
					<view class="userinfo_foot">
						<view class="userinfo_foot_item" @click="$Router.push('/pages/user/collect/collect')"
							v-if="flagInfo.tool && flagInfo.tool.data.scsp">
							<view class="">
								<text>我的关注</text>
								<view class="">
									<text>点击立即查看</text>
									<u-icon name="arrow-right" color="#AAAEBA" size="24rpx"></u-icon>
								</view>
							</view>
							<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/attention.png"
								mode="aspectFit"></image>
						</view>
						<view class="userinfo_foot_item" @click="$Router.push('/pages/user/footprint/footprint')"
							v-if="flagInfo.tool && flagInfo.tool.data.foot">
							<view class="">
								<text>我的足迹</text>
								<view class="">
									<text>历史浏览记录</text>
									<u-icon name="arrow-right" color="#AAAEBA" size="24rpx"></u-icon>
								</view>
							</view>
							<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/foot.png"
								mode="aspectFit"></image>
						</view>
					</view>
				</view>
				<view class="userinfo_padding userinfo_margintop"
					v-if="(flagInfo.tool && flagInfo.tool.data && JSON.stringify(flagInfo.tool.data) != '{}') || (memberTool && memberTool.length > 0)">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">会员工具</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push('/pages/user/coupon/coupon')"
								v-if="flagInfo.tool.data.wdhb">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_1.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.wdhb || '我的红包' }}</text>
							</view>
							<view class="tool_item" v-if="flagInfo.tool.data.yhm" @click="memCodeShow">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_2.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.yhm || "会员码" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/pay/fundtransfer/fundtransfer')"
								v-if="flagInfo.tool.data.scdp">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_3.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.scdp || "资金互转" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/pay/fundexchange/fundexchange')"
								v-if="flagInfo.tool.data.zjdh">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_4.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.zjdh || "资金兑换" }}</text>
							</view>

							<template v-if="pluginInfo.shbxt == 1 && flagInfo.tool.data.shsz">
								<view class="tool_item" v-if="userInfo.is_supplier == 0"
									@click="$Router.push('/pages/supplier/apply/apply')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_6.png"
										mode="aspectFit"></image>
									<text>{{ flagInfo.tool.data.shsz || "申请商户" }}</text>
								</view>
								<view class="tool_item" v-else-if="userInfo.is_supplier == 1"
									@click="$Router.push('/pages/supplier/platform/platform')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_6.png"
										mode="aspectFit"></image>
									<text>商户中心</text>
								</view>
							</template>
							<template v-if="pluginInfo.gbfx == 1 && flagInfo.tool.data.shdd">
								<view class="tool_item" v-if="userInfo.is_agent > 0"
									@click="$Router.push('/pages/user/agent_center/agent_center')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_7.png"
										mode="aspectFit"></image>
									<text>{{ `${baseConfig.shop.agent_name || '代理'}中心` }}</text>
								</view>
								<view class="tool_item" v-else
									@click="$Router.push('/pages/user/apply_agent/apply_agent')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_7.png"
										mode="aspectFit"></image>
									<text>{{ flagInfo.tool.data.shdd || `申请${baseConfig.shop.agent_name || '代理'}`
									}}</text>
								</view>
							</template>

							<view class="tool_item" @click="$Router.push('/pages/sub/signin/signin')"
								v-if="flagInfo.tool.data.hbrw">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_9.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.hbrw || "积分签到" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/platoon/info')"
								v-if="pluginInfo.gpcj == 1 && flagInfo.tool.data.gpcj">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_10.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.gpcj || '公排点位' }}</text>
							</view>
							<view class="tool_item" @click="openTurntable"
								v-if="pluginInfo.dzp == 1 && flagInfo.tool.data.dzpzdcj">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_11.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.dzpzdcj || '抽奖中心' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/shareholder/staff/staff')"
								v-if="pluginInfo.qyyy == 1 && flagInfo.tool.data.yggl && userInfo.is_operate_staff == 1">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_5.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.yggl || "员工管理" }}</text>
							</view>
							<template v-if="pluginInfo.gxgd == 1 && flagInfo.tool.data.gxgd">
								<view class="tool_item" v-if="userInfo.is_shareholder > 0"
									@click="$Router.push('/pages/shareholder/shareholder/shareholder')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_8.png"
										mode="aspectFit"></image>
									<text>股东管理</text>
								</view>
								<view class="tool_item" v-else @click="$Router.push('/pages/shareholder/apply/apply')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_8.png"
										mode="aspectFit"></image>
									<text>{{ flagInfo.tool.data.gxgd || "申请股东" }}</text>
								</view>
							</template>
							<view class="tool_item" @click="$Router.push('/pages/oa/contractlist/contractlist')"
								v-if="flagInfo.tool.data.wdht && userInfo.is_contract == 1">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_12.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.wdht || "我的合同" }}</text>
							</view>
							<template v-if="pluginInfo.qsps == 1 && flagInfo.tool.data.qszx">
								<view class="tool_item" @click="$Router.push('/pages/sub/rider_center/rider_center')"
									v-if="userInfo.is_rider == 1">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_13.png"
										mode="aspectFit"></image>
									<text>{{ flagInfo.tool.data.qszx || '骑手中心' }}</text>
								</view>
								<view class="tool_item" @click="$Router.push('/pages/user/apply_rider/apply_rider')"
									v-else-if="userInfo.is_rider == 0">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_13.png"
										mode="aspectFit"></image>
									<text>申请骑手</text>
								</view>
							</template>

							<template v-if="pluginInfo.jdfw == 1 && flagInfo.tool.data.sfzx">
								<view class="tool_item" v-if="userInfo.is_receiving > 0"
									@click="$Router.push('/pages/sub/receiving/receiving_center')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_17.png"
										mode="aspectFit"></image>
									<text>{{ flagInfo.tool.data.sfzx || '师傅中心' }}</text>
								</view>
								<view class="tool_item" v-else
									@click="$Router.push('/pages/sub/receiving/receiving_apply')">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_17.png"
										mode="aspectFit"></image>
									<text>师傅入驻</text>
								</view>
							</template>
							<view class="tool_item" @click="$Router.push('/pages/order/clockconfirm/clockconfirm')"
								v-if="pluginInfo.jdfw == 1 && baseConfig.shop.receiving_is_appoint == 1 && flagInfo.tool.data.zdyjz">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_16.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.zdyjz || '自定义加钟' }}</text>
							</view>
							<!-- #ifdef MP-WEIXIN -->
							<button open-type="contact" class="tool_item" border="none"
								:custom-style="{ border: 'none' }" :hair-line='false' v-if="flagInfo.tool.data.xcxkf">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/vip_15.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.xcxkf || "客服中心" }}</text>
							</button>
							<!-- #endif -->
							<view class="tool_item" @click="$Router.push('/pages/supplier/drag/drag_user')"
								v-if="pluginInfo.zyfh == 1 && flagInfo.tool.data.wzy">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/drag/drag_user.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.wzy || "微展业" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/supplier/drag/customer_list')"
								v-if="pluginInfo.zyfh == 1 && flagInfo.tool.data.wdkh">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/drag/drag_wdkh.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.wdkh || "我的客户" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/supplier/drag/follow_record')"
								v-if="pluginInfo.zyfh == 1 && flagInfo.tool.data.gjjl">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/drag/drag_gjjl.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.tool.data.gjjl || "跟进记录" }}</text>
							</view>
							<view class="tool_item" @click="$t.gotoLink(item.menu_link)" v-for="(item, i) in memberTool"
								:key="i">
								<image :src="$t.getImgUrl(item.menu_pic)"></image>
								<text>{{ item.menu_title }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.jpsc_order && JSON.stringify(flagInfo.jpsc_order.data) != '{}' && pluginInfo.jpsc == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">竞拍商城</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" style="width: 20%;position: relative;"
								@click="$Router.push('/pages/order/auctionorder/auctionorder?status=参拍中')"
								v-if="flagInfo.jpsc_order.data.cpz">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_1.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.jpsc_order.data.cpz || '参拍中' }}</text>
								<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
									:offset="[5, 15]" :value="userInfo.my_order_num_8"
									customStyle="line-height: 24rpx;"></u-badge>
							</view>
							<view class="tool_item" style="width: 20%;"
								@click="$Router.push('/pages/order/auctionorder/auctionorder?status=未获拍')"
								v-if="flagInfo.jpsc_order.data.whp">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_2.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.jpsc_order.data.whp || '未获拍' }}</text>
							</view>
							<view class="tool_item" style="width: 20%;position: relative;"
								@click="$Router.push('/pages/order/auctionorder/auctionorder?status=未支付')"
								v-if="flagInfo.jpsc_order.data.dzf">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_3.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.jpsc_order.data.dzf || '待支付' }}</text>
								<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
									:offset="[5, 15]" :value="userInfo.my_order_num_9"
									customStyle="line-height: 24rpx;"></u-badge>
							</view>
							<view class="tool_item" style="width: 20%;position: relative;"
								@click="$Router.push('/pages/order/auctionorder/auctionorder?status=已获拍')"
								v-if="flagInfo.jpsc_order.data.yhp">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_4.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.jpsc_order.data.yhp || '已获拍' }}</text>
								<u-badge numberType="overflow" type="error" max="99" color="#fff" :absolute="true"
									:offset="[5, 15]" :value="userInfo.my_order_num_10"
									customStyle="line-height: 24rpx;"></u-badge>
							</view>

							<view class="tool_item" style="width: 20%;"
								@click="$Router.push('/pages/order/auctionorder/auctionorder?status=已流拍')"
								v-if="flagInfo.jpsc_order.data.ylp">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_5.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.jpsc_order.data.ylp || '已流拍' }}</text>
							</view>

						</view>

					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.flxx && JSON.stringify(flagInfo.flxx.data) != '{}' && pluginInfo.flxx == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">分类信息</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push(`/pages/sub/cityinfo/add`)"
								v-if="flagInfo.flxx.data.fl_tcfb">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/flxx_1.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.flxx.data.fl_tcfb || "同城发布" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/sub/cityinfo/myreserve`)"
								v-if="flagInfo.flxx.data.fl_yyfw">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/flxx_2.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.flxx.data.fl_yyfw || "预约服务" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/sub/cityinfo/myinfo`)"
								v-if="flagInfo.flxx.data.fl_wdxx">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/flxx_3.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.flxx.data.fl_wdxx || "我的信息" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/sub/cityinfo/myreply`)"
								v-if="flagInfo.flxx.data.fl_wdpl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/flxx_4.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.flxx.data.fl_wdpl || "我的评论" }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.tzzt && JSON.stringify(flagInfo.tzzt.data) != '{}' && pluginInfo.xxmk == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">门店自提</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push('/pages/sub/mention/mention_apply')"
								v-if="flagInfo.tzzt.data.smqr && userInfo.mention_id <= 0">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_1.png"
									mode="aspectFill">
								</image>
								<text>申请门店</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/mention/mention_writeoff')"
								v-if="flagInfo.tzzt.data.smqr && userInfo.mention_id > 0">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_2.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.tzzt.data.smqr || "扫码确认" }}</text>
							</view>
							<view class="tool_item"
								@click="$Router.push('/pages/order/mention/mention_order?status=待提货订单')"
								v-if="flagInfo.tzzt.data.dthdd && userInfo.mention_id > 0">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_3.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.tzzt.data.dthdd || '待提货订单' }}</text>
							</view>
							<view class="tool_item"
								@click="$Router.push('/pages/order/mention/mention_order?status=已提货订单')"
								v-if="flagInfo.tzzt.data.ythdd && userInfo.mention_id > 0">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_4.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.tzzt.data.ythdd || '已提货订单' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/mention/mention_edit')"
								v-if="flagInfo.tzzt.data.xgtzxx && userInfo.mention_id > 0">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/auction_5.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.tzzt.data.xgtzxx || '信息修改' }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.sqwd && JSON.stringify(flagInfo.sqwd.data) != '{}' && pluginInfo.sqwd == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">社群微店</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item"
								@click="$Router.push(`/pages/sub/store/store_page?mid=${userInfo.id}`)"
								v-if="flagInfo.sqwd.data.wdwd">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/sqwd_1.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.sqwd.data.wdwd || "我的微店" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/store/store_manage')"
								v-if="flagInfo.sqwd.data.glsp">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/sqwd_2.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.sqwd.data.glsp || "管理商品" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/store/store_setting')"
								v-if="flagInfo.sqwd.data.wdzl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/sqwd_3.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.sqwd.data.wdzl || "微店设置" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/order/storeorder/storeorder')"
								v-if="flagInfo.sqwd.data.gldd">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/sqwd_4.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.sqwd.data.gldd || "微店订单" }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.yuncang && JSON.stringify(flagInfo.yuncang.data) != '{}' && pluginInfo.ycxt == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view> {{ flagInfo.yuncang.title || "我的云仓" }} </view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push(`/pages/sub/yc/yc_mall`)"
								v-if="flagInfo.yuncang.data.yc_wyjh">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_1.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_wyjh || "我要进货" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/yc/yc_order')"
								v-if="flagInfo.yuncang.data.yc_ddgl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_2.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_ddgl || "订单管理" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/yc/yc_team')"
								v-if="flagInfo.yuncang.data.yc_yclv">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_3.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_yclv || "代理列表" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/yc/yc_myproduct')"
								v-if="flagInfo.yuncang.data.yc_wdyc">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_4.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_wdyc || "我的云仓" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/sub/yc/yc_outlet`)"
								v-if="flagInfo.yuncang.data.yc_lscc">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_5.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_lscc || "零售出仓" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/yc/yc_exchagegoods')"
								v-if="flagInfo.yuncang.data.yc_hhgl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_6.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_hhgl || "换货管理" }}</text>
							</view>

							<view class="tool_item" @click="$Router.push('/pages/sub/yc/yc_authorization')"
								v-if="flagInfo.yuncang.data.yc_zzzs">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_7.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_zzzs || "资质证书" }}</text>
							</view>

							<view class="tool_item" @click="$Router.push('/pages/sub/yc/yc_market')"
								v-if="flagInfo.yuncang.data.yc_scsj">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_8.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.yuncang.data.yc_scsj || "市场数据" }}</text>
							</view>

							</u-grid>
						</view>
					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.live && JSON.stringify(flagInfo.live.data) != '{}' && pluginInfo.zbcj == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">直播电商</view>
							<view @click="$Router.push('/pages/live/release_form/release_form')">发布短视频</view>
						</view>
						<view class="userinfo_part_tool">
							<template v-if="flagInfo.live.data.live_zb">
								<view class="tool_item" @click="openLivePush" v-if="userInfo.is_live == 1">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/zb_1.png"
										mode="aspectFill"></image>
									<text>{{ flagInfo.live.data.live_zb || "发起直播" }}</text>
								</view>
								<view class="tool_item" @click="$Router.push('/pages/live/apply/apply')"
									v-else-if="userInfo.is_live == 0">
									<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/zb_2.png"
										mode="aspectFill">
									</image>
									<text>申请主播</text>
								</view>
							</template>
							<!-- <view class="tool_item" @click="$Router.push('/pages/live/live_pull/live_pull?uid='+userInfo.id)" v-if="userInfo.is_live == 1">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/live_1.png" mode="aspectFill"></image>
								<text>查看直播</text>
							</view> -->
							<view class="tool_item" @click="$Router.push('/pages/live/follow_list/follow_list')"
								v-if="flagInfo.live.data.live_gz">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/zb_3.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.live.data.live_gz || "我的粉丝" }}</text>
							</view>
							<view class="tool_item"
								@click="$Router.push(`/pages/live/home_page/home_page?mid=${userInfo.id}`)"
								v-if="flagInfo.live.data.live_hf">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/zb_4.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.live.data.live_hf || "我的主页" }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/live/comment_list/comment_list')"
								v-if="flagInfo.live.data.live_dd">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/zb_5.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.live.data.live_dd || "我的评论" }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.esjysc && JSON.stringify(flagInfo.esjysc.data) != '{}' && pluginInfo.esjysc == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">闲置市场</view>
							<view @click="$Router.push('/pages/sub/market/issue_market')"
								v-if="flagInfo.esjysc.data.fbxz">发布闲置</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push(`/pages/sub/market/market_list?types=1`)"
								v-if="flagInfo.esjysc.data.wfbd">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/market_2.png"
									mode="aspectFill">
								</image>
								<text>我发布的</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/market/market_list?types=2')"
								v-if="flagInfo.esjysc.data.wmcd">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/market_3.png"
									mode="aspectFill">
								</image>
								<text>我卖出的</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/order/orderlist/orderlist?types=77')"
								v-if="flagInfo.esjysc.data.wmdd">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/market_4.png"
									mode="aspectFill">
								</image>
								<text>我买到的</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/market/market_list?types=4')"
								v-if="flagInfo.esjysc.data.scxz">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/market_5.png"
									mode="aspectFill">
								</image>
								<text>收藏闲置</text>
							</view>
						</view>
					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="!isBoss && flagInfo.qiuzhi && JSON.stringify(flagInfo.qiuzhi.data) != '{}' && pluginInfo.zpxt == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">求职招聘</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="exchangeJob('boss')" v-if="flagInfo.qiuzhi.data.zpxt_wyzp">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_1.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.qiuzhi.data.zpxt_wyzp || '我要招聘' }}</text>
							</view>
							<view class="tool_item"
								@click="$Router.push(`/pages/job/resume_detail/resume_detail?id=${userInfo.id}`)"
								v-if="flagInfo.qiuzhi.data.zpxt_wdjl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_2.png"
									mode="aspectFill"></image>

								<text>{{ flagInfo.qiuzhi.data.zpxt_wdjl || '我的简历' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/post/post`)"
								v-if="flagInfo.qiuzhi.data.zpxt_ppzw">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_3.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.qiuzhi.data.zpxt_ppzw || '匹配职位' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/skill_enroll/skill_enroll`)"
								v-if="flagInfo.qiuzhi.data.zpxt_ybmpx">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_4.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.qiuzhi.data.zpxt_ybmpx || '已报名培训' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/resume_mutual/resume_mutual`)"
								v-if="flagInfo.qiuzhi.data.zpxt_ytjl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_5.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.qiuzhi.data.zpxt_ytjl || '已投简历' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/interview_list/interview_list`)"
								v-if="flagInfo.qiuzhi.data.zpxt_sdmsyq">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_6.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.qiuzhi.data.zpxt_sdmsyq || '面试邀请' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/interested_list/interested_list`)"
								v-if="flagInfo.qiuzhi.data.zpxt_dwgxq">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_7.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.qiuzhi.data.zpxt_dwgxq || '对我感兴趣' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/collect_list/collect_list`)"
								v-if="flagInfo.qiuzhi.data.zpxt_wdsc">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/job_8.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.qiuzhi.data.zpxt_wdsc || '我的收藏' }}</text>
							</view>

						</view>
					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="isBoss && flagInfo.zhaopin && JSON.stringify(flagInfo.zhaopin.data) != '{}' && pluginInfo.zpxt == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">求职招聘</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="exchangeJob('user')" v-if="flagInfo.zhaopin.data.zpxt_wyqz">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_1.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_wyqz || '我要求职' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/company_edit/company_edit`)"
								v-if="flagInfo.zhaopin.data.zpxt_qyzl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_2.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_qyzl || '企业资料' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/post_list/post_list`)"
								v-if="flagInfo.zhaopin.data.zpxt_gwbj">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_3.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_gwbj || '岗位编辑' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/recruit_list/recruit_list`)"
								v-if="flagInfo.zhaopin.data.zpxt_pprc">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_4.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_pprc || '匹配人才' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/interview_list/interview_list`)"
								v-if="flagInfo.zhaopin.data.zpxt_msyq">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_5.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_msyq || '面试邀请' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/resume_mutual/resume_mutual`)"
								v-if="flagInfo.zhaopin.data.zpxt_ysjl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_6.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_ysjl || '已收简历' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/collect_list/collect_list`)"
								v-if="flagInfo.zhaopin.data.zpxt_wscd">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_7.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_wscd || '我收藏的' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push(`/pages/job/job_fairs/job_fairs`)"
								v-if="flagInfo.zhaopin.data.zpxt_zph">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/boss_8.png"
									mode="aspectFill"></image>
								<text>{{ flagInfo.zhaopin.data.zpxt_zph || '招聘会' }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.esjysc && JSON.stringify(flagInfo.esjysc.data) != '{}' && pluginInfo.esjysc == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view class="">圈子动态</view>
							<view @click="$Router.push('/pages/sub/trends/trends_add')"
								v-if="flagInfo.esjysc.data.fbxz">发布动态</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push('/pages/sub/trends/trends_my')"
								v-if="flagInfo.esjysc.data.wfbd">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/trends_1.png"
									mode="aspectFill">
								</image>
								<text>我发布的</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/trends/trends_follow?types=1')"
								v-if="flagInfo.esjysc.data.wmcd">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/trends_2.png"
									mode="aspectFill">
								</image>
								<text>我的粉丝</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/trends/trends_follow?types=2')"
								v-if="flagInfo.esjysc.data.wmdd">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/trends_3.png"
									mode="aspectFill">
								</image>
								<text>我的关注</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/trends/trends_comment')"
								v-if="flagInfo.esjysc.data.scxz">
								<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/trends_4.png"
									mode="aspectFill">
								</image>
								<text>我的评论</text>
							</view>
						</view>
					</view>
				</view>
				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.wcrm && JSON.stringify(flagInfo.wcrm.data) != '{}' && pluginInfo.wcrm == 1 && userInfo.is_staff == 1">
					<view class="userinfo_part">
						<view class="userinfo_part_title">
							<view>CRM</view>
						</view>
						<view class="userinfo_part_tool">
							<view class="tool_item" @click="$Router.push('/pages/sub/crm/client_public')"
								v-if="flagInfo.wcrm.data.ghkh">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_1.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.wcrm.data.ghkh || '公海客户' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/crm/client_my')"
								v-if="flagInfo.wcrm.data.wdkh">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_2.png"
									mode="aspectFill">
								</image><text>{{ flagInfo.wcrm.data.wdkh || '我的客户' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/sub/crm/client_transaction')"
								v-if="flagInfo.wcrm.data.wdcj">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_3.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.wcrm.data.wdcj || '我的成交' }}</text>
							</view>
							<view class="tool_item"
								@click="$Router.push('/pages/pay/cashflow/cashflow?iden=staff_amount')"
								v-if="flagInfo.wcrm.data.wdsy">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/yuncang_4.png"
									mode="aspectFill">
								</image>
								<text>{{ flagInfo.wcrm.data.wdsy || '我的收益' }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="userinfo_padding userinfo_margintop"
					v-if="flagInfo.m_data && flagInfo.m_data.data && JSON.stringify(flagInfo.m_data.data) != '{}'">
					<view class="userinfo_team">
						<view class="userinfo_team_title">
							<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/uni/user/invite_title.png"
								mode="heightFix">
							</image>
							<view class="" @click="$Router.push('/pages/user/team/team')">
								<text>查看详情</text>
								<u-icon name="arrow-right" color="#AAAEBA" size="24rpx"></u-icon>
							</view>
						</view>
						<view class="userinfo_team_box">
							<view class="userinfo_team_item userinfo_team_item1">
								<text>我的好友</text>
								<text>{{ userInfo.ynumber }}</text>
								<view class="" v-if="flagInfo.m_data.data.m_1"
									@click="$Router.push('/pages/user/share/share')">
									立即查看
								</view>
							</view>
							<view class="userinfo_team_item userinfo_team_item2">
								<text>我的团队</text>
								<text>{{ userInfo.znumber }}</text>
								<view class="" v-if="flagInfo.m_data.data.m_1"
									@click="$Router.push('/pages/user/share/share')">
									立即查看
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="userinfo_padding userinfo_margintop">
					<view class="userinfo_part"
						v-if="flagInfo.foot && flagInfo.foot.data && JSON.stringify(flagInfo.foot.data) != '{}'">
						<view class="userinfo_part_tool" style="padding-bottom: 20rpx;">
							<view class="tool_item"
								@click="$Router.push(`/pages/chat/customer_service/customer_service`)"
								v-if="flagInfo.foot.data.kfzx">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/other_1.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.foot.data.kfzx || '客服中心' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/order/invoiceapply/invoiceapply')"
								v-if="flagInfo.foot.data.fpfw">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/other_2.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.foot.data.fpfw || '发票服务' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/setting/addresslist/addresslist')"
								v-if="flagInfo.foot.data.dzgl">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/other_3.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.foot.data.dzgl || '地址管理' }}</text>
							</view>
							<view class="tool_item" @click="$Router.push('/pages/user/feedback/feedback')"
								v-if="flagInfo.foot.data.yjfk">
								<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/other_4.png"
									mode="aspectFit"></image>
								<text>{{ flagInfo.foot.data.yjfk || '意见反馈' }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="" style="padding: 0 20rpx;">
					<shopdetailRecommend v-if="flagInfo.recommend.data == 'true'"></shopdetailRecommend>
				</view>
				<view class="" style="width: 100% ;height: 200rpx;"></view>

			</view>
			<!-- #ifdef MP-WEIXIN -->
			<privacyAgreement></privacyAgreement>
			<!-- #endif -->

			<memCode ref="memCode" v-if="flagInfo.tool.data.yhm"></memCode>
			<turnTable ref="turnTable" v-if="pluginInfo.dzp == 1 && flagInfo.tool.data.dzpzdcj"></turnTable>

		</z-paging>
		<u-transition :show="loadingShow" duration="300" :mode="loadingShow ? '' : 'fade'">
			<cl-loading></cl-loading>
		</u-transition>
		<myTabbar :currentIndex="4" v-if="tabbarShow"></myTabbar>
	</view>
</template>

<script>
import myTabbar from "@/components/common/my_tabbar.vue"
// #ifdef MP-WEIXIN
import privacyAgreement from "@/components/common/privacy_agreement";
// #endif
import appletLogin from "@/components/common/applet_login.vue";
import uniCopy from "@/common/copy.js";
import memCode from "@/components/common/mem_code.vue";
import turnTable from "@/components/common/turntable.vue";
import shopdetailRecommend from "@/components/common/shopdetail_recommend.vue";
export default {
	data() {
		return {
			loadingShow: true,
			memberTool: [],
			flagInfo: {
				top: { data: {} },
				money: { data: {} },
				tool: { data: {} },
				recommend: { data: {} },
				my_order: { data: {} },
				m_data: { data: {} },
				m_tx: { data: {} },
				live: { data: {} },
			},
			isAppletPlatform: false,
			orderList: [],
			nicknameShow: false,
			form: {
				nickname: "",
			},
			refresherStatus: 0,
			dataList: [],
			tabbarShow: true,
		};
	},
	components: {
		myTabbar,
		memCode,
		turnTable,
		appletLogin,
		shopdetailRecommend,
		// #ifdef MP-WEIXIN
		privacyAgreement,
		// #endif
	},
	computed: {
		topHeight() {
			let height = 334;
			if (this.moneyList && this.moneyList.length == 0) {
				height = 210;
			}
			height = this.$u.getPx(height + 'rpx') + this.navHeight;
			return height + "px";
		},
		moneyList() {
			if (this.flagInfo && this.flagInfo.money) {
				let arr = [];
				for (let i in this.flagInfo.money.data) {
					let o = {};
					if (i == "yr") {
						o.iden = "money";
					} else if (i == "yj") {
						o.iden = "amount";
					} else if (i == "jf") {
						o.iden = "integral";
					} else if (i == "xlbcc") {
						o.iden = "coin_storage";
					} else if (i == "xlbhd") {
						o.iden = "coin";
					} else {
						o.iden = i;
					}
					o.title = this.flagInfo.money.data[i];
					arr.push(o);
				}
				return arr;
			} else {
				return []
			}
		},
	},
	async onLoad() {
		uni.hideTabBar({});
		// #ifdef MP-WEIXIN
		this.isAppletPlatform = true;
		await this.$onAppletLogin;
		// #endif
		this.$api.getUser.getUserInfoFlag({}).then(res => {
			this.loadingShow = false;
			if (res.code == 200) {
				if (res.result.flag) {
					this.flagInfo = JSON.parse(res.result.flag)
				}
				this.memberTool = res.result.member_tool;
			}
		})
	},
	onShow() {
		// #ifdef MP-WEIXIN
		if (
			this.isLogin &&
			this.userInfo.tid == 0 &&
			this.baseConfig.shop.reg_permission == "1"
		) {
			uni.showModal({
				title: "温馨提示",
				content: "请先前往绑定推荐人",
				confirmText: "立即前往",
				cancelText: "我再想想",
				success: (data) => {
					if (data.confirm) {
						setTimeout(() => {
							this.$Router.push(`/pages/user/bindShare/bindShare`);
						}, 500);
					}
				},
			});
		}
		// #endif
		this.$store.dispatch("getUserInfo");
		if (this.isLogin) {
			this.getOrderList();
		}
		if (this.shopCardNum >= 0) {
			this.$t.setTabBarNumber(this.shopCardNum, 'shopcard')
		}
	},
	methods: {
		exchangeJob(iden) {
			let loginIm = () => {
				this.$imSdk.logout().then(() => {
					if (
						iden == "boss" &&
						this.userInfo &&
						this.userInfo.company_im
					) {
						this.$imSdk.login('company');
					} else {
						this.$imSdk.login();
					}
				});
			};
			if (iden == "boss" && this.userInfo.is_company == 0) {
				uni.showModal({
					title: "温馨提示",
					content: "当前账号未进行企业认证，是否立即前往",
					confirmText: "立即前往",
					cancelText: "暂不前往",
					success: (res) => {
						if (res.confirm) {
							this.$Router.push("/pages/job/company_approve/company_approve");
						} else if (res.cancel) { }
					},
				});
			} else if (iden == "boss" && this.userInfo.is_company == 1) {
				uni.showModal({
					title: "温馨提示",
					content: "是否切换企业招聘端",
					confirmText: "立即切换",
					cancelText: "暂不切换",
					success: (res) => {
						if (res.confirm) {
							loginIm();
							this.$u.vuex("isBoss", true);
						} else if (res.cancel) { }
					},
				});
			} else if (iden == "user") {
				uni.showModal({
					title: "温馨提示",
					content: "是否切换人才求职端",
					confirmText: "立即切换",
					cancelText: "暂不切换",
					success: (res) => {
						if (res.confirm) {
							loginIm();
							this.$u.vuex("isBoss", false);
						} else if (res.cancel) { }
					},
				});
			}
		},
		openLivePush() {
			uni.showActionSheet({
				itemList: ["发起手机直播", "使用obs直播"],
				success: ({ tapIndex }) => {
					if (tapIndex == 0) {
						this.generateLive()
					} else {
						this.$Router.push("/pages/live/live_obs/live_obs");
					}
				},
				fail: (error) => { }
			})
		},
		generateLive() {
			// #ifdef APP || APP-PLUS
			// this.$Router.push('/pages/live/live_push/live_push')
			uni.navigateTo({
				url: "/pages/live/live_push/live_push",
			});
			// #endif
			// #ifndef APP || APP-PLUS
			this.$t.toast("请在APP中打开");
			// #endif
		},
		gotoShare() {
			if (this.isAppletPlatform && this.userInfo.is_applets_login == 0) {
				this.openAppletLogin();
			} else {
				this.$Router.push("/pages/user/share/share");
			}
		},
		closeAppletLogin() {
			this.tabbarShow = true;
		},
		openAppletLogin() {
			this.tabbarShow = false;
			this.$refs.appletLogin.openAppletLogin();
		},
		openTurntable() {
			this.$refs.turnTable.init({ con: "指定抽奖" });
		},
		memCodeShow() {
			this.$refs.memCode.showCode();
		},
		confirmNickname() {
			let params = {
				nickname: this.form.nickname,
			};
			if (this.form.nickname.length > 6) {
				this.$t.toast("昵称最多六个中文");
				return false;
			}
			this.$api.getUser.saveUserInfo(params).then((res) => {
				if (res.code == 200) {
					this.$store.dispatch("getUserInfo").then((res) => { });
					this.$t.toast("保存成功");
					this.nicknameShow = false;
					this.form = {
						nickname: "",
					};
				}
			});
		},
		copyUserName() {
			uniCopy({
				content: this.userInfo.username,
				success: (res) => {
					this.$t.toast("复制成功");
				},
				error: (e) => {
					this.$t.toast(e || "复制失败");
				},
			});
		},
		callScan() {
			this.$t.callScan().then((codeStr) => { });
		},
		getOrderList() {
			this.$api.getOrder
				.getOrderList({ is_pay: 1, page_size: 5 })
				.then((res) => {
					if (res.code == 200) {
						let arr = [];
						res.result.forEach((item, i) => {
							let obj = {};
							if (item.product && item.product[0]) {
								obj.id = item.id;
								obj.p_title = item.product[0].title;
								obj.p_piclink = item.product[0].piclink;
								obj.status = item.status;
								obj.time = this.$imSdk.wxTime(item.update_time * 1000);
								obj.number = item.product.length;
								arr.push(obj);
							}
						});
						this.orderList = arr;
					}
				});
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getPay.getRechargeLists(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result);
			// this.$refs.paging.completeByNoMore(res.result,true);   //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
	},
};
</script>

<style lang="scss" scoped>
//#ifdef MP-WEIXIN
/deep/button {
	margin-left: 0;
	margin-right: 0;
}

//#endif
.userinfo {
	width: 100%;

	.userinfo_box {
		@include flex-center(row, space-around, center);
		padding: 0 25rpx;

		.userinfo_box_item {
			@include flex-center(column, center, center);
			gap: 14rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #212120;

			>image {
				width: 54rpx;
				height: 54rpx;
			}
		}
	}

	.userinfo_padding {
		padding: 0 25rpx;
	}

	.userinfo_margintop {
		margin-top: 20rpx;
	}

	.userinfo_body {
		width: 100%;
	}

	.userinfo_top_setting {
		width: 100%;
		margin-bottom: 50rpx;
		@include flex-center(row, flex-end, center);

		>view {
			@include flex-center(row, center, center);
		}
	}

	.userinfo_top_name_info_top_setting {
		margin-left: auto;
		@include flex-center(row, flex-end, center);
		padding: 20rpx 25rpx;

		>view {
			margin-left: 20rpx;
		}
	}

	.userinfo_top_name {
		width: 100%;
		margin-bottom: 40rpx;
		@include flex-center(row, flex-start, center);
		margin-top: 25rpx;

		.userinfo_top_name_avatar {
			position: relative;
			width: 110rpx;
			height: 110rpx;
			margin-right: 20rpx;

			>image {
				width: 110rpx;
				height: 110rpx;
				border-radius: 50%;
				border: 2rpx solid transparent;
				border-color: rgba(255, 255, 255, 0.5);
			}

			>text {
				max-width: 110rpx;
				min-width: 84rpx;
				height: 36rpx;
				@include flex-center(row, center, center);
				text-align: center;
				font-weight: bold;
				font-size: 20rpx;
				padding: 0 10rpx;
				color: #fff;
				background: #f99e8d;
				border-radius: 40rpx;
				position: absolute;
				bottom: -15rpx;
				left: 0;
				right: 0;
				white-space: nowrap;
			}
		}

		.userinfo_top_name_info {
			flex: 1;

			.userinfo_top_name_info_top {
				@include flex-center(row, flex-start, center);


			}

			.top_name_nickname {
				font-size: 30rpx;
				font-weight: 800;
				color: #333333;
				line-height: 2;
			}

			.top_name_editname {
				@include flex-center(row, flex-start, center);
				font-size: 30rpx;
				font-weight: 800;
				color: #333333;
				line-height: 2;

				.nicknamepop {
					width: 600rpx;
					padding: 30rpx 30rpx 0 30rpx;
					@include flex-center(column, center, center);
					border-radius: 20rpx;
					overflow: hidden;
					background-color: #fff;

					.pop_title {
						font-size: 36rpx;
						color: #323233;
						margin-bottom: 30rpx;
						padding-bottom: 0 !important;
					}

					.pop_btn {
						width: 100%;
						height: 90rpx;
						margin-top: 30rpx;
						border-top: 2rpx solid #f3f3f3;
						@include flex-center(row, space-between, center);

						>view {
							font-size: 32rpx;
							width: 50%;
							@include flex-center(row, center, center);
							text-align: center;
						}

						>view:nth-of-type(1) {
							color: #323233;
							border-right: 2rpx solid #f3f3f3;
						}

						>view:nth-of-type(2) {
							color: #ee0a24;
						}
					}
				}
			}

			.userinfo_top_name_bottom {
				@include flex-center(row, flex-start, center, wrap);

				.top_name_invite {
					height: 40rpx;
					background: linear-gradient(90deg, #DAE6FE 0%, rgba(218, 230, 254, 0.11) 100%);
					border-radius: 20rpx;
					padding: 0 10rpx;
					@include flex-center(row, flex-start, center);
					margin-right: 10rpx;

					>image {
						width: 28rpx;
						height: 28rpx;
						margin-right: 5rpx;
					}

					>text {
						font-weight: 400;
						font-size: 22rpx;
						color: #232C3B;
					}
				}
			}
		}

		.userinfo_top_name_btn {
			@include flex-center(row, center, center);
			gap: 6rpx;
			flex-shrink: 0;
			padding: 12rpx 20rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #EBEDE5;
			background: linear-gradient(93deg, #6F768E 0%, #192A44 100%);
			border-radius: 50rpx;
		}
	}

	.userinfo_top_money {
		height: 140rpx;
		overflow: auto;

		.money_box {
			width: 100%;
			display: -webkit-inline-box;
			padding-top: 35rpx;

			.money_item_2 {
				width: 50% !important;
			}

			.money_item_3 {
				width: 33% !important;
			}

			.money_item {
				width: 180rpx;
				@include flex-center(column, center, center);
				position: relative;

				.money_item_tip {
					position: absolute;
					top: -30rpx;
					left: 50%;
					font-size: 18rpx;
					padding: 0 16rpx;
					white-space: nowrap;
					font-weight: normal;
					background: linear-gradient(90deg, #f36f5c 0%, #e94b55 100%);
					color: #ffffff;
					height: 28rpx;
					line-height: 28rpx;
					border-radius: 14rpx;

					&:before {
						content: "";
						width: 0px;
						height: 0px;
						border-top: 6rpx solid #fd6c51;
						border-right: 14rpx solid transparent;
						position: absolute;
						top: 28rpx;
						left: 14rpx;
					}
				}

				>text:nth-of-type(1) {
					font-size: 30rpx;
					color: #111111;
					line-height: 40rpx;
				}

				>text:nth-of-type(2) {
					font-size: 24rpx;
					font-weight: 400;
					color: #686667;
				}
			}
		}
	}

	.userinfo_top_order {
		width: 100%;
		background: #ffffff;
		border-radius: 20rpx;

		.userinfo_top_share {
			width: 100%;
			height: 135rpx;
			background: url("https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/share_bj.png") no-repeat;
			background-size: 100% 100%;
			padding: 0 25rpx;
			@include flex-center(row, space-between, center);

			>view:nth-of-type(1) {
				flex: 1;
				@include flex-center(column, flex-start, flex-start);

				>text:nth-of-type(1) {
					font-size: 30rpx;
					font-weight: 500;
					color: #f6d399;
				}

				>text:nth-of-type(2) {
					font-size: 24rpx;
					color: #f6d399;
				}
			}

			>view:nth-of-type(2) {
				width: 122rpx;
				height: 54rpx;
				font-size: 24rpx;
				font-weight: 500;
				color: #583e12;
				background: linear-gradient(90deg, #fae3ab 0%, #faebce 100%);
				border-radius: 27rpx;
				text-align: center;
				line-height: 54rpx;
			}
		}

		.order_box {
			width: 100%;
			padding-top: 30rpx;
			@include flex-center(row, flex-start, center);

			.order_scrool {
				flex: 1;
				overflow: hidden;

				.order_scrool_box {
					width: 100%;
					display: -webkit-inline-box;
				}
			}

			.order_item {
				width: 140rpx;
				padding: 16rpx 0;
				@include flex-center(column, center, center);

				.order_item_icon {
					width: 46rpx;
					height: 46rpx;
					margin-bottom: 10rpx;
					position: relative;

					>image {
						width: 100%;
						height: 100%;
					}
				}

				>text {
					font-size: 24rpx;
					color: #333333;
					line-height: 2;
				}
			}

			.order_item_all {
				width: 150rpx;
				position: relative;
			}

			.order_item_all::before {
				width: 6rpx;
				height: 80rpx;
				background: linear-gradient(90deg,
						rgba(241, 241, 241, 0) 0%,
						#f1f1f1 100%);
				position: absolute;
				left: 0;
				content: "";
			}
		}

		.order_list_swiper {
			width: 100%;
			padding: 0 15rpx;

			.swiper_order_item {
				background: #f9f9f9;
				padding: 20rpx;
				border-radius: 12rpx;
				@include flex-center(row, flex-start, center);

				.swiper_order_item_piclink {
					width: 100rpx;
					height: 100rpx;
					position: relative;
					margin-right: 20rpx;

					>image {
						width: 100%;
						height: 100%;
						border-radius: 12rpx;
					}

					>text {
						position: absolute;
						bottom: 0;
						right: 0;
						background-color: #e7382c;
						color: #fff;
						font-size: 20rpx;
						border-top-left-radius: 25rpx;
						padding: 0 5rpx 0 10rpx;
					}
				}

				.swiper_order_item_content {
					flex: 1;
					overflow: hidden;
					margin-right: 35rpx;

					>view:nth-of-type(1) {
						margin-bottom: 10rpx;
						@include flex-center(row, flex-start, center);

						>text:nth-of-type(1) {
							font-size: 26rpx;
							font-weight: 500;
							color: #111111;
							margin-right: 10rpx;
						}

						>text:nth-of-type(2) {
							font-size: 24rpx;
							color: #aaaeba;
						}
					}

					>view:nth-of-type(2) {
						font-size: 24rpx;
						color: #aaaeba;
						@include line_overflow(100%);
					}
				}

				.swiper_order_item_btn {
					width: 125rpx;
					height: 50rpx;
					background: #ffffff;
					border-radius: 25rpx;
					border: 2rpx solid #e7382c;
					font-size: 24rpx;
					font-weight: 500;
					color: #e7382c;
					text-align: center;
					line-height: 50rpx;
				}
			}
		}
	}

	.userinfo_foot {
		width: 100%;
		padding: 30rpx 0;
		background: #ffffff;
		border-radius: 20rpx;
		@include flex-center(row, flex-start, center);

		.userinfo_foot_item:nth-of-type(1) {
			position: relative;
		}

		.userinfo_foot_item:nth-of-type(1)::after {
			position: absolute;
			width: 1rpx;
			height: 60rpx;
			background: #e8e8ea;
			right: 0;
			content: "";
		}

		.userinfo_foot_item {
			flex: 1;
			padding: 0 30rpx;
			@include flex-center(row, flex-start, center);

			>view:nth-of-type(1) {
				margin-right: auto;

				>text {
					line-height: 40rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #111111;
				}

				>view {
					margin-top: 8rpx;
					@include flex-center(row, flex-start, center);

					>text {
						line-height: 33rpx;
						font-size: 24rpx;
						color: #aaaeba;
					}
				}
			}

			>image {
				width: 82rpx;
				height: 82rpx;
			}
		}
	}

	.userinfo_part {
		width: 100%;
		background: #ffffff;
		border-radius: 20rpx;

		.userinfo_part_title {
			height: 85rpx;
			margin: 0 20rpx;
			@include flex-center(row, space-between, center);
			// border-bottom: 2rpx solid #f3f3f3;

			>view:nth-of-type(1) {
				font-size: 32rpx;
				font-weight: 500;
				color: #111111;
			}

			>view:nth-of-type(2) {
				font-size: 24rpx;
				color: #aaaeba;
			}
		}

		.userinfo_part_tool {
			padding-bottom: 30rpx;
			@include flex-center(row, flex-start, center, wrap);

			.tool_item {
				width: 25%;
				background-color: transparent;
				@include flex-center(column, center, center);

				.tool_item_image {
					position: relative;

					>image {
						width: 46rpx;
						height: 46rpx !important;
						margin-top: 30rpx;

					}
				}

				>image {
					width: 46rpx;
					height: 46rpx;
					margin: 30rpx 0 12rpx 0;
				}

				>text {
					font-weight: 500;
					font-size: 24rpx;
					color: #333333;
				}
			}

			button {
				padding: 0;
			}

			uni-button[plain] {
				border: 0;
			}

			button::after {
				border: none;
			}

			uni-button:after {
				display: none;
			}
		}
	}

	.userinfo_team {
		background: url("https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/invite_bj.png") no-repeat;
		background-size: 800rpx auto;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 22rpx;

		.userinfo_team_title {
			@include flex-center(row, flex-start, center);

			>image {
				height: 36rpx;
				margin-right: auto;
			}

			>view {
				@include flex-center(row, center, center);

				>text {
					font-size: 24rpx;
					color: #aaaeba;
				}
			}
		}

		.userinfo_team_box {
			width: 100%;
			margin-top: 30rpx;
			@include flex-center(row, space-between, center);

			.userinfo_team_item1 {
				background: url("https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/invite_1.png") no-repeat;
				background-size: 100% 100%;

				>text:nth-of-type(1) {
					color: #ef4d44;
				}

				>view:nth-of-type(1) {
					border: 2rpx solid #ef4d44;
					color: #ef4d44;
				}
			}

			.userinfo_team_item2 {
				background: url("https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/invite_2.png") no-repeat;
				background-size: 100% 100%;

				>text:nth-of-type(1) {
					color: #f3a242;
				}

				>view:nth-of-type(1) {
					border: 2rpx solid #f3a242;
					color: #f3a242;
				}
			}

			.userinfo_team_item {
				width: 322rpx;
				height: 236rpx;
				border-radius: 20rpx;
				padding: 30rpx 30rpx 0 30rpx;
				@include flex-center(column, flex-start, flex-start);

				>text:nth-of-type(1) {
					font-size: 30rpx;
					font-weight: 600;
					line-height: 42rpx;
				}

				>text:nth-of-type(2) {
					line-height: 56rpx;
					font-size: 40rpx;
					font-weight: 600;
					color: #333333;
					margin: 20rpx 0;
				}

				>view:nth-of-type(1) {
					width: 132rpx;
					height: 48rpx;
					border-radius: 25rpx;
					font-size: 24rpx;
					font-weight: 600;
					text-align: center;
					line-height: 48rpx;
				}
			}
		}
	}
}
</style>