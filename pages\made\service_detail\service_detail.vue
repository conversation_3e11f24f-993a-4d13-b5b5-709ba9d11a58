<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" @scroll="onScroll" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title=" " mpWeiXinShow :bgColor="!is_bgChange ? 'transparent' : '#fff'" :autoBack="true"
					:fixed="true" class="custom_navbar">
					<view slot="left" v-if="!is_bgChange">
						<image class="navicon" src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/lefticon.png"
							mode="scaleToFill" />
					</view>
					<view slot="right">

						<u-icon size="30rpx" v-if="is_bgChange"
							name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/share_icon1.png"></u-icon>

						<image v-else class="navicon"
							src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/shareicon.png"
							mode="scaleToFill" />
					</view>
				</cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<!-- 轮播图区域 -->
				<view class="carousel_container">
					<u-swiper :list="swiperList" :autoplay="false" height="750rpx" radius="0">
					</u-swiper>
					<view class="service_details_container">
						<!-- 价格和标题区域 -->
						<view class="price_section">
							<view class="price_container">
								<view class="price_main">
									<text class="price_symbol">￥</text>
									<text class="price_number">1880</text>
									<text class="price_decimal">.00</text>
								</view>
								<view class="price_original">￥2256.00</view>
							</view>
							<view class="sales_info">已售2000+</view>
						</view>

						<!-- 服务标题 -->
						<view class="service_title">律师陪购全程套餐</view>
						<!-- 推荐理由 -->
						<view class="recommend_section">
							<view class="recommend_content">
								<view class="recommend_icon_container">
									<u-icon name="thumb-up-fill" color="#E7382C" size="40rpx"></u-icon>
								</view>
								<view class="recommend_label">推荐理由</view>
								<view class="recommend_text">专业律师护航，让房屋交易手续安全、高效、零风险</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 保障和服务信息 -->
				<view class="guarantee_section service_details_container">
					<view class="guarantee_item">
						<view class="guarantee_label">保障</view>
						<view class="guarantee_text">支持7天无理由退货</view>
					</view>
					<view class="guarantee_item">
						<view class="guarantee_label">服务</view>
						<view class="service_features">
							<view class="feature_item">
								<u-icon name="checkmark-circle" color="#006AFC" size="30rpx"></u-icon>
								<text class="feature_text">专业服务</text>
							</view>
							<view class="feature_item">
								<u-icon name="checkmark-circle" color="#006AFC" size="30rpx"></u-icon>
								<text class="feature_text">安全高效</text>
							</view>
							<view class="feature_item">
								<u-icon name="checkmark-circle" color="#006AFC" size="30rpx"></u-icon>
								<text class="feature_text">品质保证</text>
							</view>
						</view>
					</view>

				</view>

				<!-- 服务详情 -->
				<view class="service_details_section">
					<view class="section_title_container">
						<view class="title_line_left"></view>
						<view class="section_title">服务详情</view>
						<view class="title_line_right"></view>
					</view>
					<view class="service_description" v-html="serviceDetailHtml">
						<u-parse :tagStyle="{span:''}" :content="serviceDetailHtml" :lazyLoad="true"
							loadingImg="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/product/default_piclink.jpg"></u-parse>
					</view>
				</view>


			</view>
			<view slot="bottom">
				<view class="bottom_purchase_container">
					<view class="purchase_button" @click="handlePurchase">
						<text class="purchase_text">立即购买</text>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			swiperList: [
				'https://img2.baidu.com/it/u=3970917306,880187556&fm=253&fmt=auto&app=120&f=JPEG?w=655&h=808',
				'https://img2.baidu.com/it/u=363858033,1221485415&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067'
			],
			serviceDetailHtml: `
				<p style="color: #333333; font-size: 24rpx; line-height: 24rpx; margin-bottom: 20rpx;">
					律师会审查房屋交易的合法性，查验、核实交易房屋的法定证明文件，审查、修改房屋交易合同文本，协助委托人进行交易谈判，并协助签订房屋买卖合同，还会提供签约后的法律咨询服务。
				</p>
				<p style="color: #333333; font-size: 24rpx; line-height: 24rpx;">
					还会协助（或代理）委托人申请办理公积金贷款、商业贷款，督促交易相对人履行合同义务，协助（或代理）房屋交付验收、办理房屋入住手续，以及申请办理房屋所有权、土地使用权登记和交易过户手续等。
				</p>
			`,
			screenHeight: 0,
			is_bgChange: false,

		};
	},
	computed: {},
	onLoad() {
		this.screenHeight = uni.getSystemInfoSync().windowHeight;
	},
	methods: {
		onScroll(e) {
			console.log(e.detail.scrollTop, this.screenHeight / 3);
			if (e.detail.scrollTop > this.screenHeight / 3) {
				this.is_bgChange = true;
			} else {
				this.is_bgChange = false;
			}
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		handlePurchase() {
			// 跳转到订单确认页面
			uni.navigateTo({
				url: '/pages/made/orderconfirm/orderconfirm?service_id=1&service_name=律师陪购全程套餐&price=1880.00'
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.navicon {
		width: 66rpx;
		height: 66rpx;
	}

	.loading_list {
		width: 100%;
		@include flex-center(column, flex-start, flex-start);
		gap: 20rpx;

		>view {
			width: 100%;
		}

		// 轮播图区域
		.carousel_container {}

		.service_details_container {
			background: #FFFFFF;
			padding: 30rpx 25rpx;

		}

		// 价格和标题区域
		.price_section {

			@include flex-center(row, space-between, flex-start);

			.price_container {
				@include flex-center(row, flex-start, flex-end);
				gap: 20rpx;

				.price_main {
					@include flex-center(row, flex-start, baseline);

					.price_symbol {
						font-size: 30rpx;
						font-weight: bold;
						color: #EE1616;
						line-height: 30rpx;
					}

					.price_number {
						font-size: 46rpx;
						font-weight: bold;
						color: #EE1616;
						line-height: 46rpx;
					}

					.price_decimal {
						font-size: 30rpx;
						font-weight: bold;
						color: #EE1616;
						line-height: 30rpx;
					}
				}

				.price_original {
					font-size: 26rpx;
					color: #999999;
					text-decoration: line-through;
					line-height: 26rpx;
				}
			}

			.sales_info {
				font-size: 26rpx;
				color: #999999;
				line-height: 26rpx;
			}
		}

		// 服务标题
		.service_title {
			font-size: 34rpx;
			font-weight: bold;
			color: #111111;
			line-height: auto;
			margin-top: 20rpx;
			@include text_overflow(100%, 2);

		}

		// 推荐理由
		.recommend_section {
			background: #FEF2F3;
			border-radius: 8rpx;
			padding: 20rpx;
			margin-top: 20rpx;

			.recommend_content {
				@include flex-center(row, flex-start, center);
				gap: 8rpx;


				.recommend_label {
					font-size: 24rpx;
					font-weight: bold;
					color: #E7382C;
				}

				.recommend_text {
					@include text_overflow(100%, 1);
					flex: 1;
					font-size: 22rpx;
					color: #E7382C;
				}
			}
		}

		// 保障和服务信息
		.guarantee_section {
			@include flex-center(column, flex-start, flex-start);
			gap: 35rpx;


			.guarantee_item {
				width: 100%;
				@include flex-center(row, flex-start, center);
				gap: 29rpx;

				.guarantee_label {
					font-size: 26rpx;
					color: #999999;
					line-height: 26rpx;
				}


				.guarantee_text {
					flex: 1;
					@include text_overflow(100%, 1);
					font-size: 26rpx;
					color: #111111;
					line-height: 26rpx;
				}
			}

			.service_features {
				@include flex-center(row, flex-start, center);
				gap: 30rpx;

				.feature_item {
					@include flex-center(row, flex-start, center);
					gap: 10rpx;

					.feature_text {
						font-size: 24rpx;
						color: #111111;
						line-height: 24rpx;
					}
				}
			}
		}

		// 服务详情
		.service_details_section {
			width: 750rpx;
			background: #FFFFFF;
			padding: 32rpx 25rpx;

			.section_title_container {
				@include flex-center(row, center, center);
				gap: 20rpx;
				margin-bottom: 32rpx;

				.title_line_left,
				.title_line_right {
					width: 50rpx;
					height: 2rpx;
					background: #D8D8D8;
				}

				.section_title {
					font-size: 30rpx;
					font-weight: bold;
					color: #111111;
					line-height: 30rpx;
				}
			}

			.service_description {
				font-size: 24rpx;
				color: #333333;
				line-height: 36rpx;
			}
		}

		// 选择理由图片
		.reason_image_container {
			width: 750rpx;
			height: 1073rpx;

			.reason_image {
				width: 100%;
				height: 100%;
			}
		}
	}
}

// 底部购买按钮
.bottom_purchase_container {
	width: 751rpx;
	height: 128rpx;
	background: #FFFFFF;
	box-shadow: 0px 3px 6px 0px rgba(207, 207, 207, 0.16);
	@include flex-center(row, center, center);
	padding: 20rpx 25rpx;
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

	.purchase_button {
		width: 700rpx;
		height: 88rpx;
		background: #006AFC;
		border-radius: 15rpx;
		box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.04);
		@include flex-center(row, center, center);

		.purchase_text {
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 32rpx;
		}
	}
}
</style>