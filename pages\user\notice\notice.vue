<template>
	<view class="page_notice">
		<z-paging ref="paging" :safe-area-inset-bottom="true" bgColor="#FAFAFA" @query="queryList"
			:refresher-status.sync="refresherStatus" :hide-loading-more-when-no-more-and-inside-of-paging="false">
			<view slot="top">

				<view class="page_notice_top">
					<view class="" style="width:100%;"
						:style="{ height: statusBarHeight ? statusBarHeight + 'px' : 'auto' }"></view>
					<view class="page_notice_top_header"
						:style="{ height: navigationBarHeight ? navigationBarHeight + 'px' : 'auto', width: menuButtonLeft + 'px' }">
						<view class="page_notice_top_header_left" @click="navClick">
							<u-icon name="arrow-left" size="40rpx" color="#333333" v-if="showBack"></u-icon>
							<text>消息中心</text>
							<text>({{ totalUnreadNumber }})</text>
						</view>
					</view>
					<view class="" style="margin-top: 10rpx;">
						<u-search placeholder="请输入搜索关键词" height="64rpx" bgColor="#F5F5F5" v-model="title"
							:showAction="false" @search="confirmSearch()"></u-search>
					</view>
				</view>

			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="page_notice_body">
				<view class="page_notice_part" v-show="!isSearch">
					<view class="page_notice_part_title">
						<text>平台消息</text>
						<u-badge numberType="overflow" type="error" max="99" color="#fff"
							:value="stystemUnreadNumber"></u-badge>

					</view>
					<view class="page_notice_part_list">
						<view class="page_notice_item page_notice_item1" v-for="(item, i) in systemNotice" :key="i"
							@click="gotoNotice(item)" v-show="!isSearch">
							<image :src="$t.getImgUrl(item.piclink)" mode="aspectFill"></image>
							<view class="page_notice_item_content">
								<view class="page_notice_item_content_title">
									<text class="page_notice_item_content_title_p">{{ item.title }}</text>
									<u-badge numberType="overflow" type="error" max="99" color="#fff"
										:value="item.view_number"></u-badge>
									<text class="page_notice_item_content_title_time" v-if="item.time">{{ item.time
									}}</text>
								</view>
								<view class="page_notice_item_content_tip">
									{{ item.content || '暂无通知消息' }}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="page_notice_part" style="margin-bottom: 100rpx;"
					v-if="pluginInfo.btkfxt == 1 || pluginInfo.imhyjsnt == 1">
					<view class="page_notice_part_title">
						<text>聊天信息</text>
						<u-badge numberType="overflow" type="error" max="99" color="#fff"
							:value="chatUnreadNumber"></u-badge>
					</view>
					<view class="page_notice_part_list">
						<view class="page_notice_item page_notice_item2" v-for="(item, c) in conversationResult"
							:key="c" @click="gotoConversation(item)">
							<image :src="$t.getImgUrl(item.avatar)" mode="aspectFill"></image>
							<view class="page_notice_item_content">
								<view class="page_notice_item_content_title">
									<text class="page_notice_item_content_title_p">{{ item.nickname }}</text>
									<u-badge numberType="overflow" type="error" max="99" color="#fff"
										:value="item.unread"></u-badge>
									<text class="page_notice_item_content_title_time" v-if="item.lastTime">{{
										item.lastTime }}</text>
								</view>
								<view class="page_notice_item_content_tip">
									{{ item.messageForShow || "&nbsp" }}
								</view>
							</view>
						</view>

						<u-empty v-if="conversationResult && conversationResult.length == 0" mode="data"
							icon="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/empty_data.png"
							text="暂无聊天信息哦"></u-empty>
					</view>
				</view>
			</view>

			<view slot="loadingMoreNoMore"></view>
		</z-paging>
		<u-transition :show="loadingShow" duration="300" :mode="loadingShow ? '' : 'fade'">
			<cl-loading></cl-loading>
		</u-transition>
	</view>
</template>

<script>
export default {
	name: "page_notice",
	data() {
		return {
			isInit: false,
			showBack: false,
			loadingShow: true,
			isSearch: false,
			searchList: [],
			platform: "",
			refresherStatus: 0,
			systemNotice: [
				{ id: 1001, "title": "注册通知", "piclink": "https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/notice_icon_3.png", "types": 1, "data": {}, "view_number": 0 },
				{ id: 1002, "title": "订单消息", "piclink": "https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/notice_icon_4.png", "types": 2, "data": {}, "view_number": 0 },
				{ id: 1003, "title": "审核通知", "piclink": "https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/notice_icon_1.png", "types": 3, "data": {}, "view_number": 0 },
				{ id: 1004, "title": "资金变动", "piclink": "https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/notice_icon_2.png", "types": 4, "data": {}, "view_number": 0 }
			],
			title: "",
		}
	},
	watch: {},
	computed: {
		conversationResult() {
			if (this.isSearch) {
				return this.searchList
			} else {
				return this.conversationList
			}
		},
		totalUnreadNumber() {
			return this.stystemUnreadNumber + this.chatUnreadNumber;
		},
		stystemUnreadNumber() {
			let number = 0;
			let arr = this.systemNotice || []
			arr.forEach((item, i) => {
				number = number + item.view_number
			})
			return number;
		},
		chatUnreadNumber() {
			let number = 0;
			if (this.im && this.im.conversationList && this.im.conversationList.length > 0) {
				let arr = this.im.conversationList || []
				arr.forEach((item, i) => {
					number = number + item.unreadCount
				})
			}
			return number;
		},
		conversationList() {
			let arr = []
			if (this.im && this.im.conversationList && this.im.conversationList.length > 0) {
				arr = this.im.conversationList || []
			}
			let n_arr = [];
			arr.forEach((item, i) => {
				let obj = {};
				obj.conversationID = item.conversationID;
				obj.type = item.type;
				if (item.userProfile) {
					obj.nickname = item.userProfile.nick || item.userProfile.userID;
				} else if (item.groupProfile) {
					obj.nickname = item.groupProfile.name;
				} else {
					obj.nickname = item.from
				}
				obj.messageForShow = this.$imSdk.getMessageForShow(item.lastMessage);
				if (item.lastMessage && item.lastMessage.lastTime) {
					obj.lastTime = this.$imSdk.wxTime(item.lastMessage.lastTime * 1000);
				} else {
					obj.lastTime = "";
				}
				obj.unread = item.unreadCount;
				if (item.userProfile && item.userProfile.avatar) {
					obj.avatar = item.userProfile.avatar
				} else if (item.groupProfile) {
					obj.avatar = item.groupProfile.avatar || 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/967made/group_icon.jpg'
				}
				if (item.conversationID.indexOf('admin') >= 0) {
					if (obj.avatar) {
						obj.avatar = obj.avatar
					} else if (this.baseConfig.shop.logo) {
						this.avatar = this.$t.getImgUrl(this.baseConfig.shop.logo)
					} else {
						obj.avatar = this.$configInfo.customerServiceAvatar
					}
				} else {
					obj.avatar = obj.avatar || this.$configInfo.maleDefaultAvatar
				}
				n_arr.push(obj)
			})
			return n_arr;
		},
	},
	onLoad() {
		if (this.$Route.path == '/pages/user/notice/notice') {
			this.showBack = true;
		}
		this.platform = uni.$u.platform
	},
	onShow() {
		if (this.$refs.paging) {
			this.$refs.paging.reload(false);
		}
		if (this.shopCardNum >= 0) {
			this.$t.setTabBarNumber(this.shopCardNum, 'shopcard')
		}
	},
	methods: {
		navClick() {
			if (this.showBack) {
				this.$t.gotoBack()
			}
		},
		confirmSearch() {
			if (this.title) {
				this.isSearch = true
				let val1 = this.title.toUpperCase();
				let val2 = this.title.toLowerCase();
				let arr = this.conversationList;
				let n_arr = [];
				arr.forEach((item, i) => {
					if (item.nickname.indexOf(val1) >= 0 || item.conversationID.indexOf(val1) >= 0 || item.messageForShow.indexOf(val1) >= 0) {
						n_arr.push(item)
					} else if (item.nickname.indexOf(val2) >= 0 || item.conversationID.indexOf(val2) >= 0 || item.messageForShow.indexOf(val2) >= 0) {
						n_arr.push(item)
					}
				})
				this.searchList = n_arr;
			} else {
				this.isSearch = false;
			}
		},
		gotoNotice(item) {
			this.$Router.push(`/pages/user/notice_detail/notice_detail?types=${item.types}&title=${item.title}&piclink=${encodeURIComponent(item.piclink)}`)
		},
		gotoConversation(item) {
			console.log(item)
			let conversationID = item.conversationID.replace(item.type, '')
			if (conversationID.indexOf('admin') >= 0) {
				this.$Router.push(`/pages/chat/chat_page/chat_page?id=${conversationID}&type=service`)
			} else {
				this.$Router.push(`/pages/chat/chat_page/chat_page?id=${conversationID}&type=${item.type}`)
			}
		},
		queryList(pageNo, pageSize) {
			this.$api.getUser.getUserSystemNoticeInfo({}).then(res => {
				if (res.code == 200) {
					res.result.forEach((item, i) => {
						if (item.data && item.data.id) {
							item.time = this.$imSdk.wxTime(item.data.created_time * 1000)
							item.content = item.data.content;
						}
						let index = this.systemNotice.findIndex((notice, n) => {
							return notice.types == item.types;
						})
						if (index >= 0) {
							this.$set(this.systemNotice, index, item);
						} else {
							this.systemNoticse.push(item)
						}
					})
					if (this.$refs.paging) {
						this.$refs.paging.complete(res.result);
					}
					this.loadingShow = false;
					if (!this.isInit) {
						this.$nextTick(() => {
							if (this.$configInfo.imOpen === true && this.userInfo.im) {
								this.$imSdk.intIMSdk().then(() => {
									this.$imSdk.login().then(res => {
										console.log('登录成功')
									})
								})
							}
						})
					}
					this.isInit = true;
					// setTimeout(() => {
					// }, 1000);
				} else {
					this.$refs.paging.complete(false);
				}
			})
		},
	},
}
</script>
<style>
page {
	background-color: #FAFAFA;
}
</style>
<style lang="scss" scoped>
.page_notice {
	width: 100%;

	.page_notice_top {
		width: 100%;
		padding: 0 25rpx 15rpx 25rpx;
		background-color: #fff;

		.page_notice_top_header {
			width: 100%;
			/* #ifndef MP-WEIXIN */
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			/* #endif */
			@include flex-center(row, space-between, center);

			.page_notice_top_header_left {
				line-height: 1;
				@include flex-center(row, flex-start, flex-end);

				>text:nth-of-type(1) {
					font-size: 36rpx;
					font-weight: 500;
					color: #0E1225;
					margin-right: 10rpx;
				}

				>text:nth-of-type(2) {
					font-size: 24rpx;
					font-weight: 500;
					color: #0E1225;
				}
			}

			.page_notice_top_header_right {
				@include flex-center(row, flex-end, center);

				>image {
					width: 24rpx;
					height: 22rpx;
					margin-right: 10rpx;
				}

				>text {
					font-size: 24rpx;
					color: #333333;
				}
			}
		}
	}

	.page_notice_body {
		.page_notice_part {
			width: 100%;
			padding: 0 25rpx;

			.page_notice_part_title {
				padding: 30rpx 0 20rpx 0;
				@include flex-center(row, flex-start, center);

				>text:nth-of-type(1) {
					font-size: 30rpx;
					font-weight: 500;
					color: #0E1225;
					margin-right: 10rpx;
				}
			}

			.page_notice_part_list {
				width: 100%;

				.page_notice_item1:not(:last-of-type) {
					margin-bottom: 20rpx;
				}

				.page_notice_item2:not(:last-of-type) {
					border-bottom: 2rpx solid #f9f9f9;
				}

				.page_notice_item1 {
					border-radius: 20rpx;
					background-color: #fff;
				}

				.page_notice_item2 {
					background-color: #fff;
				}

				.page_notice_item {
					padding: 30rpx 20rpx;
					@include flex-center(row, flex-start, center);

					>image {
						width: 82rpx;
						height: 82rpx;
						margin-right: 20rpx;
						border-radius: 10rpx;
					}

					.page_notice_item_content {
						flex: 1;
						height: 82rpx;
						overflow: hidden;
						@include flex-center(column, space-between, flex-start);

						.page_notice_item_content_title {
							width: 100%;
							@include flex-center(row, flex-start, center);

							.page_notice_item_content_title_p {
								font-size: 28rpx;
								font-weight: 500;
								color: #333333;
								margin-right: 10rpx;
							}

							.page_notice_item_content_title_time {
								font-size: 24rpx;
								color: #CDCDCD;
								margin-left: auto;
							}
						}

						.page_notice_item_content_tip {
							font-size: 26rpx;
							color: #9B9B9B;
							@include line_overflow(100%);
						}
					}
				}
			}

			.page_chat_part_list {
				width: 100%;
				background: #FFFFFF;
				border-radius: 20px
			}
		}

		.chat_list_item {
			padding: 0 25rpx;
			height: 150rpx;
			@include flex-center(row, flex-start, center);

			>image {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}

			>.chat_list_item_right {
				flex: 1;
				height: 100%;
				font-weight: normal;
				border-bottom: 1px solid #efefef;
				@include flex-center(column, center, center);

				.chat_item_title {
					width: 100%;
					@include flex-center(row, flex-start, center);
					margin-bottom: 10rpx;

					>text:nth-of-type(1) {
						flex: 1;
						@include line_overflow(300rpx);
						margin-right: 10px;
						font-size: 30rpx;
						color: #000000;
					}

					>text:nth-of-type(2) {
						font-size: 24rpx;
						color: #c4c4c4;
					}
				}

				.chat_item_content {
					width: 100%;
					@include flex-center(row, flex-start, center);

					>text:nth-of-type(1) {
						flex: 1;
						font-size: 28rpx;
						@include line_overflow(300rpx);
						margin-right: 10px;
						color: #888888;
					}

					>view {
						background-color: #fa6527;
						color: #fff;
						font-size: 24rpx;
					}
				}
			}
		}
	}
}
</style>