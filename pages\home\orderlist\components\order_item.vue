<template>
	<view class="order_item" :style="{'--baseColor':baseColor}">
		<view class="order_item_top" @click="gotoOrderDetail">
			<view class="order_item_top_store" v-if="info.types == 55">
				跑腿 - {{ info.run_types == 1 ? '帮我送' : info.run_types == 2 ? '帮我取' : '帮我买' }}
			</view>
			<view class="order_item_top_store" v-else-if="info.types == 77">
				<image :src="$t.getImgUrl(baseConfig.shop.logo)" mode="aspectFill"></image>
				<view class="">
					闲置交易
				</view>
			</view>
			<view class="order_item_top_store" v-else>
				<image v-if="info.sid === 0 || info.sid === '0'" :src="$t.getImgUrl(baseConfig.shop.logo)" mode="aspectFill"></image>
				<image v-else :src="$t.getImgUrl(info.sid_logo)" mode="aspectFill"></image>
				<view>{{ info.sid_cn }}</view>
				<u-icon name="arrow-right" size="20rpx" color="#999999"></u-icon>
			</view>
			<text style="margin-left:auto">{{ statusCn }}</text>
		</view>
		<orderDetailRiderMap :orderInfo="info" v-if="isShowOrderDetailRiderMap"></orderDetailRiderMap>
		<view class="order_item_center" v-else-if="info.types == 55">
			<!-- 跑腿订单 -->
			<view class="order_item_errand_address">
				<view class="order_item_errand_cell">
					<view class="order_item_errand_cell_top">
						<text class="order_item_errand_cell_label" style="background: #FF653D">{{ info.run_types == 1 ||
							info.run_types == 2 ? '取' : '收' }}</text>
						<text class="order_item_errand_cell_title">{{ info.mail_address }}</text>
					</view>
					<view class="order_item_errand_cell_sub">
						<text>{{ info.mail_name }}</text>
						<text>{{ info.mail_tel }}</text>
					</view>
				</view>

				<view class="order_item_errand_cell" v-if="info.run_types == 1 || info.run_types == 2">
					<view class="order_item_errand_cell_top">
						<text class="order_item_errand_cell_label" style="background: #4B4B4B">收</text>
						<text class="order_item_errand_cell_title">{{ info.mail_address2 }}</text>
					</view>
					<view class="order_item_errand_cell_sub">
						<text>{{ info.mail_name2 }}</text>
						<text>{{ info.mail_tel2 }}</text>
					</view>
				</view>

				<view class="order_item_errand_cell" v-else>
					<view class="order_item_errand_cell_top">
						<text class="order_item_errand_cell_label" style="background: #4B4B4B">买</text>
						<text class="order_item_errand_cell_title">{{ info.mail_address3 || '就近购买' }}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="order_item_center" v-else-if="info.types == 45">
			<!-- 活动订单 -->
			<view class="center_pro" v-for="(pro, index) in info.product" :key="index">
				<view class="center_pro_center">
					<view class="center_pro_center_title" @click="gotoProductDetail(pro.pid)">{{ pro.title }}</view>
					<view class="center_pro_center_desc" v-if="pro.sku_cn">活动时间：{{ $u.timeFormat(pro.sku_cn, "yyyy-mm-dd hh: MM")}} </view>
					<view class="center_pro_center_desc">报名人数：{{ info.sum_number }} </view>
					<view class="center_pro_center_desc">核销码：{{ info.write_code }}
						<text class="center_pro_center_desc_btn" :style="{ background: baseColor ? `linear-gradient(105deg, ${baseColor} 27%, ${$t.getOpacityColor(baseColor, 0.7)} 84%)` : `linear-gradient(105deg, #fc2e38 27%, #fd4c74 84%)` }"
							@click="copyKey(info.write_code)">复制</text>

					</view>
					<view class="center_pro_center_btn" v-if="!isSupplier && !isRider && !isStore">
						<template>
							<view @click="setOrderReturn(pro)" v-if="pro.order_return">{{ pro.order_return }}</view>
						</template>
					</view>
				</view>
				<view class="center_pro_right">
					<view class="center_pro_right_price" v-if="Number(pro.price) > 0">
						<text class="center_pro_right_price_icon">￥</text>
						<text class="center_pro_right_price_int">{{ $t.getIntDec(pro.price, 'int') }}</text>
						<text class="center_pro_right_price_dec">{{ $t.getIntDec(pro.price, 'dec') }}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="order_item_center" v-else>
			<view class="center_pro" v-for="(pro, index) in info.product" :key="index">
				<image :src="$t.getImgUrl(pro.piclink)" mode="aspectFill" @click="gotoProductDetail(pro)"></image>
				<view class="center_pro_center">
					<view class="center_pro_center_title" @click="gotoProductDetail(pro)">{{ pro.title }}</view>
					<view class="center_pro_center_desc" v-if="pro.sku_cn">{{ pro.sku_cn }} </view>
					<view class="center_pro_center_btn" v-if="!isSupplier && !isRider && !isStore && !isLifting && info.types != 39 && info.types != 44">
						<!-- <view v-if="info.types_cn">{{info.types_cn}}</view> -->
						<template>
							<view v-if="pro.mid > 0 && (pro.m_nickname || pro.m_username)">微店：{{ pro.m_nickname ||
								pro.m_username }}</view>
							<view @click="setOrderReturn(pro)" v-if="pro.order_return">{{ pro.order_return }}</view>
							<!-- <view @click="cancelReturn(pro)" v-if="(pro.status == 1 || pro.status == 2 || pro.status == 3) ">取消退货</view> -->
							<view @click="gotoLink(`/pages/order/orderReview/orderReview?id=${pro.id}`)"
								v-if="info.status == '已完成' && pro.status == 0 && (pro.types == 0 || pro.types == 35 || pro.types == 1 || pro.types == 10 || pro.types == 11 || pro.types == 38)">
								{{ pro.is_review == 0 ? "待评价" : "已评价" }}
							</view>
						</template>
					</view>
				</view>
				<view class="center_pro_right">
					<view class="center_pro_right_price" v-if="pro.types != 6 && Number(pro.price) > 0">
						<text class="center_pro_right_price_icon">￥</text>
						<text class="center_pro_right_price_int">{{ $t.getIntDec(pro.price, 'int') }}</text>
						<text class="center_pro_right_price_dec">{{ $t.getIntDec(pro.price, 'dec') }}</text>
					</view>
					<text class="center_pro_right_sum">x{{ pro.number }}</text>
				</view>
			</view>
		</view>
		<view class="order_item_bottom" v-if="showBottom">
			<view class="orderItem_reserve" v-if="isSupplier || isRider || isStore || isLifting">
				<u-collapse :border="false">

					<u-collapse-item title="下单用户信息" name="1" :border="false" v-if="isSupplier || isRider || isStore || isLifting">
						<view class="collapseItem" v-if="info.uid_cn">
							<view class="s1">下单用户：</view>
							<view class="s2">{{ info.uid_cn }}</view>
						</view>
						<view class="collapseItem" v-if="isRiderOrder && info.receiving_time > 0">
							<view class="s1">用户选择送达时间：</view>
							<view class="s2">{{ $u.timeFormat(info.receiving_time, "mm月dd日 hh:MM") }}~{{
								$u.timeFormat(Number(info.receiving_time) + 3600, "hh:MM") }}</view>
						</view>
						<view class="collapseItem" v-if="`${info.mail_province || ''}${info.mail_city || ''}${info.mail_area || ''}${info.mail_town || ''}` && info.types != 14"
							@click="goToNavigation(info.mail_latitude, info.mail_longitude, info.mail_address)">
							<view class="s1">{{ info.run_types == 1 || info.run_types == 2 ? '取货' : '收货' }}地区：</view>
							<view class="s2">{{ `${info.mail_province || ""}${info.mail_city || ""}${info.mail_area ||
								""}${info.mail_town || ""}` }}</view>
						</view>
						<view class="collapseItem" v-if="info.mail_address && info.types != 14" @click="goToNavigation(info.mail_latitude, info.mail_longitude, info.mail_address)">
							<view class="s1">{{ info.run_types == 1 || info.run_types == 2 ? '取货' : '收货' }}地址：</view>
							<view class="s2">{{ info.mail_address }}
								<u-icon name="map" color="#8a8a8a" size="14"></u-icon>
							</view>
						</view>
						<template v-if="info.types == 55">

							<view class="collapseItem" v-if="(info.run_types == 1 || info.run_types == 2) && `${info.mail_province2 || ''}${info.mail_city2 || ''}${info.mail_area2 || ''}${info.mail_town2 || ''}`"
								@click="goToNavigation(info.mail_latitude2, info.mail_longitude2, info.mail_address2)">
								<view class="s1">收货地区：</view>
								<view class="s2">{{ `${info.mail_province2 || ""}${info.mail_city2 ||
									""}${info.mail_area2 || ""}${info.mail_town2 || ""}` }}</view>
							</view>
							<view class="collapseItem" v-if="(info.run_types == 1 || info.run_types == 2) && info.mail_address2" @click="goToNavigation(info.mail_latitude2, info.mail_longitude2, info.mail_address2)">
								<view class="s1">收货地址：</view>
								<view class="s2">{{ info.mail_address2 }}
									<u-icon name="map" color="#8a8a8a" size="14"></u-icon>
								</view>
							</view>
							<view class="collapseItem" v-if="info.run_types == 3 && `${info.mail_province3 || ''}${info.mail_city3 || ''}${info.mail_area3 || ''}${info.mail_town3 || ''}`"
								@click="goToNavigation(info.mail_latitude3, info.mail_longitude3, info.mail_address3)">
								<view class="s1">购买地址：</view>
								<view class="s2">{{ `${info.mail_province3 || ""}${info.mail_city3 ||
									""}${info.mail_area3 || ""}${info.mail_town3 || ""}` }}</view>
							</view>
							<view class="collapseItem" v-if="info.run_types == 3 && info.mail_address3" @click="goToNavigation(info.mail_latitude3, info.mail_longitude3, info.mail_address3)">
								<view class="s1">购买地址：</view>
								<view class="s2">{{ info.mail_address3 }}
									<u-icon name="map" color="#8a8a8a" size="14"></u-icon>
								</view>
							</view>
							<view class="collapseItem" v-if="info.run_types == 3 && !info.mail_address3">
								<view class="s1">购买地址：</view>
								<view class="s2">就近购买</view>
							</view>
							<view class="collapseItem" v-if="info.run_class && info.run_weight">
								<view class="s1">物品重量：</view>
								<view class="s2">{{ info.run_class }} &nbsp{{ Number(info.run_weight) }}公斤</view>
							</view>
							<view class="collapseItem" v-if="info.run_remarks">
								<view class="s1">{{ info.run_types == 3 ? '购买' : '' }}备注：</view>
								<view class="s2">{{ info.run_remarks }}</view>
							</view>
							<view class="collapseItem" v-if="info.run_types == 3 && info.run_predict_money > 0">
								<view class="s1">预估商品费：</view>
								<view class="s2">{{ $u.priceFormat(info.run_predict_money, 2) }}</view>
							</view>
						</template>

						<template v-if="info.types == 14">
							<view class="collapseItem" v-if="info.mail_tel">
								<view class="s1">手机号：</view>
								<view class="s2">{{ info.mail_tel }}</view>
							</view>
							<view class="collapseItem" v-if="info.receiving_time">
								<view class="s1">预约时间：</view>
								<view class="s2">{{ $u.timeFormat(info.receiving_time, "yyyy-mm-dd hh:MM") }}</view>
							</view>
						</template>

					</u-collapse-item>
					<u-collapse-item title="门店信息" name="2" :border="false" v-if="isRider && info.rider_extract_ar && info.rider_extract_ar.id">
						<view class="collapseItem" v-if="info.rider_extract_ar">
							<view class="s1">门店标题：</view>
							<view class="s2">{{ info.rider_extract_ar.title }}</view>
						</view>
						<view class="collapseItem" v-if="`${info.rider_extract_ar.province || ''}${info.rider_extract_ar.city || ''}${info.rider_extract_ar.area || ''}${info.rider_extract_ar.town || ''}`"
							@click="goToNavigation(info.rider_extract_ar.latitude, info.rider_extract_ar.longitude, info.rider_extract_ar.title)">
							<view class="s1">门店地区：</view>
							<view class="s2">{{ `${info.rider_extract_ar.province || ""}${info.rider_extract_ar.city ||
								""}${info.rider_extract_ar.area || ""}${info.rider_extract_ar.town || ""}` }}</view>
						</view>
						<view class="collapseItem" v-if="info.rider_extract_ar.add" @click="goToNavigation(info.rider_extract_ar.latitude, info.rider_extract_ar.longitude, info.rider_extract_ar.title)">
							<view class="s1">门店地址：</view>
							<view class="s2">{{ info.rider_extract_ar.add }}
								<u-icon name="map" color="#8a8a8a" size="14"></u-icon>
							</view>
						</view>
					</u-collapse-item>
				</u-collapse>
			</view>
			<view class="orderItem_reserve" v-if="info.is_chain == 1 && info.is_pay == 1 && !isSupplier && !isLifting">
				<u-collapse :border="false" :value="['1']">
					<u-collapse-item title="取餐信息" name="1" :border="false">
						<view class="cell_part_item">
							<view class="cell_part_item_left">
								<text>取餐码：</text>
							</view>
							<view class="cell_part_item_right">
								<text>{{ info.chain_code }}</text>
								<u-button :customStyle="{ width: 'auto', margin: '0 20rpx', height: '40rpx' }" color="#FF3B3B" size="mini" text="复制" @click="copyKey(info.write_code)"></u-button>
								<u-icon @click="openPainter" size="18" color="#8a8a8a" name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/page/user_share_ewm.png"></u-icon>
							</view>
						</view>
					</u-collapse-item>
				</u-collapse>
			</view>
			<view class="orderItem_reserve" v-if="info.write_code && info.is_pay == 1 && !isSupplier && !isLifting">
				<u-collapse :border="false" :value="['1']">
					<u-collapse-item title="核销信息" name="1" :border="false">
						<view class="cell_part_item">
							<view class="cell_part_item_left">
								<text>核销码：</text>
							</view>
							<view class="cell_part_item_right">
								<text>{{ info.write_code }}</text>
								<u-button :customStyle="{ width: 'auto', margin: '0 20rpx', height: '40rpx' }" color="#FF3B3B" size="mini" text="复制" @click="copyKey(info.write_code)"></u-button>
								<u-icon @click="openPainter" size="18" color="#8a8a8a" name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/page/user_share_ewm.png"></u-icon>
							</view>
						</view>
						<view class="collapseItem" v-if="info.types == 10 || info.types == 11">
							<view class="s1">已核销次数：</view>
							<view class="s2">{{ info.write_complete_number }}</view>
						</view>
						<view class="collapseItem" v-if="info.types == 10 || info.types == 11">
							<view class="s1">总核销次数：</view>
							<view class="s2">{{ info.write_number }}</view>
						</view>
					</u-collapse-item>
				</u-collapse>
			</view>
			<view class="order_item_sum" v-if="isRiderOrder">
				<view style="width:100%;display: flex;justify-content: space-between;align-items: center;margin-left: 0;">
					<view style="color: #999999 ;" v-if="statusCn == '待接单'">下单时间
						{{ $u.timeFormat(info.created_time, 'yyyy-mm-dd hh: MM')}}
					</view>
					<view style="color: #999999 ;" v-else-if="statusCn == '配送中' && !isRider && info.mail_time > 0">接单时间
						{{ $u.timeFormat(info.mail_time, 'yyyy-mm-dd hh:MM') }}
					</view>
					<view style="color: #999999 ;" v-else-if="statusCn == '配送中' && isRider && info.mail_time > 0">
						预计送达时间
						{{ $u.timeFormat(Number((info.rider_overtime || 0) * 60) + Number(info.mail_time), 'yyyy-mm-dd hh: MM')}}
					</view>
					<view style="color: #999999 ;" v-else-if="info.complete_time && info.complete_time > 0">送达时间
						{{ $u.timeFormat(info.complete_time, 'yyyy-mm-dd hh:MM') }}
					</view>
					<view style="color: #999999 ;" v-else>&nbsp</view>
					<view style="color: #333333 ; margin-left:auto">合计
						<text class="order_item_sum_icon">￥</text>
						<text class="order_item_sum_money_int">{{ $t.getIntDec(info.money, 'int') }}</text>
						<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.money, 'dec') }}</text>
					</view>
					<view style="color: #333333 ;margin-left:10rpx" v-if="info.score_rob > 0">
						<text class="order_item_sum_icon">{{ baseConfig.shop.integral_cn || "积分" }}</text>
						<text class="order_item_sum_money_int">{{ $t.getIntDec(info.score_rob, 'int') }}</text>
						<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.score_rob, 'dec') }}</text>
					</view>
				</view>
			</view>
			<view class="order_item_sum" v-else-if="info.types == 6">
				<view v-if="info.down_pay > 0">
					<view style="color: #999999 ;">定金实付款
						<text class="order_item_sum_icon">￥</text>
						<text class="order_item_sum_money_int">{{ $t.getIntDec(info.down_pay, 'int') }}</text>
						<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.down_pay, 'dec') }}</text>
					</view>
					<view style="color: #333333 ;">尾款实付款
						<text class="order_item_sum_icon">￥</text>
						<text class="order_item_sum_money_int">{{ $t.getIntDec(info.money, 'int') }}</text>
						<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.money, 'dec') }}</text>
					</view>
				</view>
				<view v-else-if="info.status == '已付定金'">
					<view style="color: #999999 ;">定金实付款
						<text class="order_item_sum_icon">￥</text>
						<text class="order_item_sum_money_int">{{ $t.getIntDec(info.money, 'int') }}</text>
						<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.money, 'dec') }}</text>
					</view>
				</view>
			</view>
			<view class="order_item_sum" v-else-if="info.status != '已取消'">
				<view style="color: #999999 ;" v-if="info.sum_price > 0">
					总价<text class="order_item_sum_icon">￥</text>
					<text class="order_item_sum_money_int">{{ $t.getIntDec(info.sum_price, 'int') }}</text>
					<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.sum_price, 'dec') }}</text>
				</view>
				<view style="color: #333333 ;" v-if="info.types == 37">
					实付款<text class="order_item_sum_icon">￥</text>
					<text class="order_item_sum_money_int">{{ $t.getIntDec(phaseMoney(info), 'int') }}</text>
					<text class="order_item_sum_money_dec">{{ $t.getIntDec(phaseMoney(info), 'dec') }}</text>
				</view>
				<view style="color: #333333 ;" v-else-if="info.money > 0">
					实付款<text class="order_item_sum_icon">￥</text>
					<text class="order_item_sum_money_int">{{ $t.getIntDec(info.money, 'int') }}</text>
					<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.money, 'dec') }}</text>
				</view>
				<view style="color: #333333 ;" v-if="info.score_rob > 0">
					<text class="order_item_sum_icon">{{ baseConfig.shop.integral_cn || "积分" }}</text>
					<text class="order_item_sum_money_int">{{ $t.getIntDec(info.score_rob, 'int') }}</text>
					<text class="order_item_sum_money_dec">{{ $t.getIntDec(info.score_rob, 'dec') }}</text>
				</view>
			</view>
			<view v-if="showBtn">
				<view class="order_item_btn" v-if="isSupplier">
					<view class="" v-if="info.status == '拼团进行中'">拼团进行中</view>
					<view class="order_item_btn_use"  @click="subDeliverSever3" v-if="(info.status == '已支付' || info.status == '已发货') && info.is_chain == 1">{{ info.status ==
							"已支付" ?
							"通知取餐" : "完成取餐" }}</view>
					<view class="order_item_btn_use"  @click="subSupplierConfirmReceipt()" v-if="info.status == '已支付' && info.is_shop_delivery == 1">
						开始配送
					</view>
					<view class="order_item_btn_use"  @click="subSupplierConfirmRider()" v-else-if="info.status == '已发货' && info.is_shop_delivery == 1">
						确认完成
					</view>
					<view class="order_item_btn_use"  @click="subDeliverGoods(info.status)" v-else-if="(info.status == '已支付' || info.status == '已发货') && info.types != 14 && info.types != 13
							&& info.is_chain != 1 && info.is_return != 1 && (info.is_shop_delivery == 2 || info.rider_extract_id == 0)">
						{{ info.status == "已支付" ? "确认发货" : "待收货" }}
					</view>
					<view class="" v-if="info.status == '已发货' && (info.types == 14 || info.types == 13)">待确认</view>
					<view class="order_item_btn_use"  @click="subDeliverSever" v-if="info.status == '已支付' && info.types == 14">确认预约</view>
				</view>
				<view class="order_item_btn" v-else-if="isStore">

				</view>
				<view class="order_item_btn" v-else-if="isLifting">
					<view class="order_item_btn_use"  @click="subMentionConfirm" v-if="(info.status == '已支付' || info.status == '已发货' || info.status == '用户自提') && info.is_lifting == 1">
						确认自提</view>
				</view>
				<view class="order_item_btn" v-else-if="isRider && info.types == 55">
					<view class="order_item_btn_use"  v-if="statusCn == '待接单' || statusCn == '待领单'" @click="riderConfirmReceive">确认领单</view>
					<view class="order_item_btn_use"  v-else-if="statusCn == '配送中'" @click="errandConfirmPickOrder(2)">确认送达</view>
					<view class="order_item_btn_use"  v-else-if="statusCn == '已领取'" @click="errandConfirmPickOrder(1)">确认取单</view>
				</view>
				<view class="order_item_btn" v-else-if="isRider">
					<view class="order_item_btn_use"  v-if="statusCn == '待接单'" @click="riderConfirmReceive">确认领单</view>
					<view class="order_item_btn_use"  v-else-if="statusCn == '配送中'" @click="writeCode = ''; showCode = true;">确认送达</view>
					<view class="order_item_btn_use"  v-else-if="statusCn == '已领取'" @click="errandConfirmPickOrder(1)">确认取单</view>
				</view>
				<view class="order_item_btn" v-else-if="info.types == 39">
					<view class="order_item_btn_use"  v-if="info.status == '未支付'" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">去支付</view>
					<view class="order_item_btn_use"  v-if="info.receiving_code && (info.status == '已接单' || info.status == '服务中')" @click="copyKey(info.receiving_code)">核销码 {{ info.receiving_code }}</view>
					<view class="" v-if="info.status == '已支付'">待接单</view>
					<view class="order_item_btn_use"  v-if="info.status == '已接单' || info.status == '服务中' || info.status == '已完成' || info.status == '售后中'" @click="callPhone(info.receiving_tel)">联系师傅</view>
					<view class="order_item_btn_use"  v-if="info.status == '已支付' || info.status == '已接单' || info.status == '服务中'"
						@click="$Router.push(`/pages/sub/receiving/receiving_cancel?id=${info.id}&types=user&door_price=${info.door_price}`)">
						取消订单
					</view>
					<view class="order_item_btn_use"  v-if="info.status == '已完成' && info.is_sale_service == 1" @click="$Router.push(`/pages/sub/receiving/receiving_detail?id=${info.id}&animation=1`)">申请售后
					</view>
					<view class="order_item_btn_use"  v-if="info.status == '售后中'" @click="$Router.push(`/pages/sub/receiving/receiving_detail?id=${info.id}&animation=2`)">取消售后
					</view>
				</view>
				<view class="order_item_btn" v-else-if="info.types == 44">
					<view class="order_item_btn_use"  v-if="info.status == '未支付'" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">去支付</view>
					<view class="order_item_btn_use"  v-if="info.status == '已支付' || info.status == '已接单' || info.status == '服务中'" @click="callPhone(info.receiving_tel)">联系师傅</view>
				</view>
				<view class="order_item_btn" v-else-if="info.types == 14">
					<view class="order_item_btn_use"  v-if="info.status == '未支付'" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">去支付</view>
				</view>
				<view class="order_item_btn" v-else-if="info.types == 55">
					<view class="" v-if="info.status == '未支付'" @click="cancelPay">取消支付</view>
					<view class="" v-if="info.status == '待领单'" @click="cancelPay">取消订单</view>
					<view class="order_item_btn_use"  v-if="info.status == '未支付'" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">去支付</view>
					<view class="order_item_btn_use" 
						v-if="info.receive_code && (info.run_types == 1 || info.run_types == 2) && info.run_is_check == 1 && info.is_pay == 1 && (info.status == '已领取' || info.status == '配送中')" @click="copyKey(info.receive_code)">取件码
						{{ info.receive_code }}
					</view>
					<view class="order_item_btn_use"  v-if="info.rider_code && info.run_is_check == 1 && info.is_pay == 1 && (info.status == '已领取' || info.status == '配送中')" @click="copyKey(info.rider_code)">核销码
						{{ info.rider_code }}
					</view>
				</view>
				<view class=" order_item_btn" v-else>
					<view class="" v-if="info.status == '未支付' && info.types != 18 && info.types != 37" @click="cancelPay">取消支付
					</view>
					<view class="order_item_btn_use"  v-if="info.status == '未支付' && info.types != 18" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">去支付</view>
					<view class="order_item_btn_use"  v-if="info.status == '已付一期款'" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">支付二期款</view>
					<view class="order_item_btn_use"  v-if="info.status == '已付二期款'" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">支付三期款</view>
					<view class="order_item_btn_use"  v-if="info.status == '已付三期款'" @click="gotoLink(`/pages/pay/payment/payment?id=${info.id}`)">支付尾款</view>
					<view class="" v-if="info.status == '已支付' && info.types != 37 && info.types != 45 && info.types != 55 && info.types != 77 && !isRiderOrder && info.is_chain != 1">
						待发货</view>
					<view class="" v-if="(info.status == '已发货' || info.status == '已完成') && info.mail_oid && info.mail_oid != '骑手配送' && info.mail_oid != 0 && info.types != 34 && !info.kd_ar"
						@click="gotoLink(`/pages/order/ordermail/ordermail?id=${info.id}`)">查看物流</view>
					<view class="" v-if="info.types == 34 && info.jh_status >= 4" @click="gotoLink(`/pages/order/ordermail/jh_ordermail?id=${info.id}`)">查看物流</view>
					<view class="" v-if="(info.status == '已发货' || info.status == '已完成') && info.mail_oid != null && info.mail_oid != '' && info.kd_ar && info.types != 9 && info.types != 14 && info.types != 13 && baseConfig.shop.is_multi_express"
						@click="checkMail(info.kd_ar)">查看物流</view>
					<view class="order_item_btn_use"  v-if="info.status == '已发货'" @click="confirmReceipt">{{ info.is_chain == 1 ? '确认取餐' : '确认收货' }}</view>
					<view class="" v-if="showAgainBuy" @click="gotoProductDetail(info.product[0])">再次购买</view>
					<view class="" v-if="info.is_return == 1">退货中</view>
					<view class="" v-if="info.types == 4 && info.groups && info.groups.id" @click="gotoLink(`/pages/shop/activity_grouping/activity_grouping?id=${info.id}`)">
						{{ info.groups.status == 0 ? "拼团进行中" : info.groups.status == 1 ? "拼团已完成" : "拼团已结束" }}
					</view>
					<view class="order_item_btn_use"  v-if="info.status == '已付定金' && info.types == 6" :class="[checkIsPresalePay() ? '' : 'disabled_btn']" @click="confirmPresalFinal">支付尾款</view>
					<view class="order_item_btn_use"  v-if="info.status == '已付定金' && info.types == 6" @click="cancelPresal">取消预售</view>

					<view class="order_item_btn_use"  v-if="info.types == 77 && info.delivery_type == 2 && info.is_pay == 1 && (info.status == '用户自提' || info.status == '已支付')" @click="confirmReceipt">确认收货</view>

					<view class="order_item_btn_use"  v-if="info.status == '未支付' && info.types == 18" @click="auctionPay">去支付</view>
				</view>

			</view>
		</view>
		<slot name="footer"></slot>

		<u-popup :show="showCode" @close="writeCode = ''; showCode = false" round="8" mode="center" :customStyle="{ width: '90%' }" v-if="showCode">
			<view class="contract_code">
				<view class="contract_code_tit">{{ runIndex == 1 ? '取件' : '核销' }}码</view>
				<u-code-input v-model="writeCode" :focus="false" :space="15" @finish="confirmReceiveOver" maxlength="6"></u-code-input>
			</view>
		</u-popup>

		<u-popup :show="painterShareShow" @close="painterShareShow = false" mode="center" :closeOnClickOverlay="true" :closeable="false" bgColor="transparent" v-if="writeInfo">
			<view class="pop_show">
				<l-painter css="width: 500rpx;height: 500rpx;" v-if="writeInfo">
					<l-painter-qrcode css="width: 500rpx; height: 500rpx;" :text="writeInfo"></l-painter-qrcode>
				</l-painter>
			</view>
		</u-popup>
		
		<u-popup :show="showOrderReturn" @close="showOrderReturn = false" round="8" mode="bottom" bgColor="#F7F6FB" v-if="showOrderReturn">
			<view class="order_return">
				<view class="order_return_header">
					<text>选择商品</text>
					<u-icon name="close" size="46rpx" color="#000" @click="showOrderReturn = false;"></u-icon>
				</view>
				<view class="order_return_body order_item_center">
					<scroll-view scroll-y="true" style="max-height:70vh;">
						 <u-checkbox-group v-model="checkList"  placement="column">
							<view class="center_pro" v-for="(pro, index) in info.product"  :key="index">
								<image :src="$t.getImgUrl(pro.piclink)" mode="aspectFill" @click="gotoProductDetail(pro)"></image>
								<view class="center_pro_center">
									<view class="center_pro_center_title" @click="gotoProductDetail(pro)">{{ pro.title }}</view>
									<view class="center_pro_center_desc" v-if="pro.sku_cn">{{ pro.sku_cn }} </view>
									<view class="center_pro_center_price">
										<text >数量{{ pro.number }};</text>
										<view class="center_pro_right_price" v-if="pro.types != 6 && Number(pro.price) > 0">
											<text class="center_pro_right_price_icon">￥</text>
											<text class="center_pro_right_price_int">{{ $t.getIntDec(pro.price, 'int') }}</text>
											<text class="center_pro_right_price_dec">{{ $t.getIntDec(pro.price, 'dec') }}</text>
										</view>
										
									</view>
								</view>
								<view class="center_pro_right" style="height: 160rpx;justify-content: center;">
									<u-checkbox shape="circle" :name="pro.id" size="38rpx" iconSize="30rpx" :customStyle="{margin: '0 10rpx',alignSelf:'center'}"  :activeColor="baseColor"></u-checkbox>
										
								</view>
							</view>
						 </u-checkbox-group>
					</scroll-view>
				</view>
					
				<view class="order_return_btn">
					<view class="form_btn" style="width: 100%;border-radius: 50rpx;" :style="{ background: baseColor }"
						@click="$u.throttle(orderReturnSubmit, 500)"> 下一步 </view>
				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
	import orderDetailRiderMap from "@/pages/order/components/order_detail_ridermap.vue"
	import uniCopy from "@/common/copy.js";
	export default {
		name: "order_item",
		props: {
			info: {
				type: Object,
				default () {
					return {};
				},
			},
			isAllowJump: {
				type: Boolean,
				default: true
			},
			//是否显示按钮
			showBtn: {
				type: Boolean,
				default: true
			},
			//是否显示底部
			showBottom: {
				type: Boolean,
				default: true
			},
			//是否为商户订单
			isSupplier: {
				type: Boolean,
				default: false,
			},
			//是否为骑手订单
			isRider: {
				type: Boolean,
				default: false,
			},
			//是否为微店订单
			isStore: {
				type: Boolean,
				default: false,
			},
			//是否为门店自提订单
			isLifting: {
				type: Boolean,
				default: false,
			},
		},
		data() {
			return {
				runIndex: "", // 跑腿 1 取件 2 送达 3 商家配送送达
				writeCode: "",
				showCode: false,
				show: false,
				painterUrl: "",
				writeInfo: null,
				painterShareShow: false,
				showOrderReturn:false, //申请多商品退货 弹窗
				checkList: [],
			};
		},
		options: { styleIsolation: 'shared' },
		computed: {
			isShowOrderDetailRiderMap() {
				if (this.isRiderOrder && this.statusCn == '配送中' && this.$Route.path == '/pages/order/orderlist/orderlist') {
					return true;
				} else {
					return false;
				}
			},
			isRiderOrder() {
				if (this.info.rider_extract_id > 0) {
					return true;
				} else {
					return false;
				}
			},
			statusCn() {
				if (this.info.types == 14 && (this.info.status == '已支付' || this.info.status == '已发货')) {
					return '已预约'
				} else if (this.info.status == '已完成' && (this.info.types == 11 || this.info.types == 10) && this.info.write_complete_number == 0) {
					return '待核销'
				} else if (this.info.rider_extract_id > 0) {
					if (this.info.is_shop_delivery == 1) {
						if (this.info.status == '已支付') {
							return '待配送'
						} else if (this.info.status == '已发货') {
							return '配送中'
						} else {
							return this.info.status
						}
					} else if (this.info.status == '已支付' && this.info.is_pay == 1 && this.info.rider_uid == 0 && this.info.is_return != 1) {
						return '待接单'
					} else if (this.info.status == '已发货' && this.info.rider_uid > 0) {
						return '配送中'
					} else if (this.info.is_return == 1) {
						return '退货中'
					} else {
						return this.info.status
					}
				} else if (this.info.is_chain == 1) {
					if (this.info.status == '已发货') {
						return '待取餐'
					} else if (this.info.status == '已支付') {
						return '制作中'
					} else {
						return this.info.status
					}
				} else if (this.info.types == 77 && this.info.is_school_zt == 0) {
					if (this.info.status == '已支付' && this.info.is_pay == 1 && this.info.rider_uid == 0) {
						return '待接单'
					} else if (this.info.status == '已发货' && this.info.rider_uid > 0) {
						return '配送中'
					} else {
						return this.info.status
					}
				} else {
					return this.info.status
				}
			},
			//是否显示再次购买
			showAgainBuy() {
				if (this.info.types == 37 || this.info.types == 17 || this.info.types == 45 || this.info.types == 77) {
					return false
				} else if (this.info.status == '已完成' && this.info.product && this.info.product.length > 0 && this.info.product[0].pid && this.info.product[0].pid != 0) {
					return true
				} else {
					return false
				}
			}
		},
		components: {
			orderDetailRiderMap
		},
		methods: {
			orderReturnSubmit() {
				if(this.checkList.length > 0){
					var str = this.checkList.join('@');
					this.showOrderReturn = false;
					this.gotoLink(`/pages/order/orderreturn/orderreturn?id=${str}`)
				}else{
					this.$t.toast('请选择商品')
				}
			},
			setOrderReturn(pro){
				if(pro.types == 34){
					this.gotoLink(`/pages/order/orderreturn/orderreturn?id=${pro.id}&types=34`)
				}else if(this.info.is_return == 1){
					this.gotoLink(`/pages/order/orderreturn/orderreturn?id=${pro.id}`)
				}else{
					this.showOrderReturn = true;
					this.checkList = [];
					this.checkList.push(pro.id);
				}
			},
			auctionPay() {
				this.$emit('auctionPay', this.info)
			},
			subSupplierConfirmRider() {
				this.runIndex = 3;
				this.writeCode = '';
				this.showCode = true;
			},
			subSupplierConfirmReceipt() {
				this.$api.getSupplier.getSupplierConfirmReceipt({ id: this.info.id }).then(res => {
					if (res.code == 200) {
						this.$t.toast('开始配送成功');
						setTimeout(() => {
							this.$emit('delete')
						}, 500)
					}
				})
			},
			// 多快递
			checkMail(arr) {
				if (arr && arr.length > 0) {
					let mailArr = arr.map((item, i) => {
						return item.mail_courier + '-' + item.mail_oid
					})
					uni.showActionSheet({
						itemList: mailArr,
						success: ({ tapIndex }) => {
							this.gotoLink(`/pages/order/ordermail/ordermail?id=${arr[tapIndex].id}&isMore=1`)
						},
					})
				} else {
					this.$t.toast('暂无物流信息')
				}
			},
			openPainter() {
				this.writeInfo = JSON.stringify({
					id: this.info.id,
					tel: this.info.tel,
					code: this.info.write_code,
					type: "writeJSON",
				})
				this.$nextTick(() => {
					this.painterShareShow = true;
				})
			},
			subMentionConfirm() {
				uni.showModal({
					title: "温馨提示",
					content: "是否确认完成自提?",
					confirmText: "立即确认",
					cancelText: "我再想想",
					success: (data) => {
						if (data.confirm) {
							this.$api.getMention.getExtractCompleteLifting({ id: this.info.id }).then(res => {
								if (res.code == 200) {
									this.$t.toast('确认成功');
									setTimeout(() => {
										this.$emit('delete')
									}, 500)
								}
							})
						}
					}
				});
			},
			copyKey(val) {
				uniCopy({
					content: val,
					success: (res) => {
						this.$t.toast("复制成功");
					},
					error: (e) => {
						this.$t.toast(e || "复制失败");
					},
				});
			},
			callPhone(tel) {
				if (tel) {
					uni.makePhoneCall({
						phoneNumber: tel + ''
					});
				} else {
					this.$t.toast('手机号不存在')
				}
			},
			goToNavigation(latitude, longitude, title) {
				uni.openLocation({
					latitude: Number(latitude),
					longitude: Number(longitude),
					name: title,
					scale: 15
				});
			},
			errandConfirmPickOrder(index) {
				this.runIndex = index;
				let text = this.runIndex == 1 ? '取件' : '送达'
				let apiName = this.runIndex == 1 ? 'confirmRunPickOrder' : 'confirmRiderOrderOver'
				if (this.info.run_types == 3) {
					this.$emit('openRunProofPayment')
				} else if (this.info.run_is_check == 1) {
					this.writeCode = '';
					this.showCode = true;
				} else {
					uni.showModal({
						title: "温馨提示",
						content: `是否确认立即${text}`,
						confirmText: `立即${text}`,
						cancelText: "我再想想",
						success: (data) => {
							if (data.confirm) {
								this.$api.getDelivery[apiName]({ id: this.info.id }).then(res => {
									if (res.code == 200) {
										this.$t.toast('确认成功');
										setTimeout(() => {
											this.$emit('delete')
										}, 500)
									}
								})
							}
						}
					});
				}
			},
			confirmReceiveOver() {
				if (this.runIndex == 3) {
					this.$u.throttle(() => {
						let params = {
							id: this.info.id,
							rider_code: this.writeCode
						}
						this.$api.getSupplier.getSupplierConfirmReceive(params).then(res => {
							if (res.code == 200) {
								this.$t.toast('确认成功');
								this.writeCode = "";
								this.showCode = false;
								setTimeout(() => {
									this.$emit('delete')
								}, 500)
							}
						})
					}, 500)
				} else if (this.runIndex > 0) {
					let text = this.runIndex == 1 ? '取件' : '送达'
					let apiName = this.runIndex == 1 ? 'confirmRunPickOrder' : 'confirmRiderOrderOver'
					this.$u.throttle(() => {
						let params = {
							id: this.info.id,
						}
						if (this.runIndex == 1) {
							params.receive_code = this.writeCode;
						} else {
							params.rider_code = this.writeCode;
						}
						this.$api.getDelivery[apiName](params).then(res => {
							if (res.code == 200) {
								this.$t.toast('确认成功');
								this.writeCode = "";
								this.showCode = false;
								setTimeout(() => {
									this.$emit('delete')
								}, 500)
							}
						})
					}, 500)
				} else {
					this.$u.throttle(() => {
						let params = {
							id: this.info.id,
							rider_code: this.writeCode
						}
						this.$api.getDelivery.confirmRiderOrderOver(params).then(res => {
							if (res.code == 200) {
								this.$t.toast('确认成功');
								this.writeCode = "";
								this.showCode = false;
								setTimeout(() => {
									this.$emit('delete')
								}, 500)
							}
						})
					}, 500)
				}
			},
			riderConfirmReceive() {
				uni.showModal({
					title: "温馨提示",
					content: "是否确认领单?",
					confirmText: "立即领单",
					cancelText: "我再想想",
					success: (data) => {
						if (data.confirm) {
							this.$api.getDelivery.receiveRiderOrder({ id: this.info.id }).then(res => {
								if (res.code == 200) {
									this.$t.toast('确认领单成功');
									setTimeout(() => {
										this.$emit('delete')
									}, 500)
								}
							})
						}
					}
				});
			},
			subDeliverSever() {
				var that = this;
				if (that.info.status == "已支付" && that.info.types == "14") {
					uni.showModal({
						title: "温馨提示",
						content: "是否确认预约服务?",
						confirmText: "立即预约",
						cancelText: "我再想想",
						success: (data) => {
							if (data.confirm) {
								this.$api.getSupplier.sendServe({ id: this.info.id }).then(res => {
									if (res.code == 200) {
										this.$t.toast('预约成功');
										setTimeout(() => {
											this.$emit('delete')
										}, 500)
									}
								})
							}
						}
					});
				}
			},
			subDeliverGoods(status) {
				if (status == "已支付") {
					uni.$off("supplierOrderRefresh")
					uni.$once("supplierOrderRefresh", () => {
						this.$emit('delete')
					})
					this.$Router.push(`/pages/supplier/delivery/delivery?id=${this.info.id}`)
				}
			},
			subDeliverSever3() {
				var that = this;
				if (that.info.status == "已支付" && that.info.is_chain == 1) {
					uni.showModal({
						title: "温馨提示",
						content: "是否确认通知客户取餐?",
						confirmText: "立即通知",
						cancelText: "我再想想",
						success: (data) => {
							if (data.confirm) {
								this.$api.getSupplier.sendServe({ id: this.info.id }).then(res => {
									if (res.code == 200) {
										this.$t.toast('通知成功');
										let obj = JSON.parse(JSON.stringify(this.info));
										setTimeout(() => {
											this.$emit('init', obj)
										}, 500)
									}
								})
							}
						}
					});
				} else if (that.info.is_chain == 1 && that.info.status == "已发货") {
					this.$api.getSupplier.getSupplierConfirmReceive({ id: this.info.id }).then(res => {
						if (res.code == 200) {
							this.$t.toast('确认成功');
							setTimeout(() => {
								this.$emit('delete')
							}, 500)
						}
					})
				}
			},
			cancelPresal() {
				uni.showModal({
					title: "温馨提示",
					content: "是否确认取消预售?",
					confirmText: "立即取消",
					cancelText: "我再想想",
					success: (data) => {
						if (data.confirm) {
							this.$api.getOrder.cancelPresalOrder({ oid: this.info.oid }).then(res => {
								if (res.code == 200) {
									this.$t.toast('取消成功');
									setTimeout(() => {
										this.$emit('delete')
									}, 500)
								}
							})
						}
					}
				});
			},
			confirmPresalFinal() {
				let flag = this.checkIsPresalePay();
				console.log(flag)
				if (flag) {
					uni.showModal({
						title: "温馨提示",
						content: "是否确认支付尾款?",
						confirmText: "我已确认",
						cancelText: "我再想想",
						success: (data) => {
							if (data.confirm) {
								this.$api.getOrder.payPresalFinal({ oid: this.info.oid }).then(res => {
									if (res.code == 200) {
										this.$t.toast('即将前往支付');
										setTimeout(() => {
											this.gotoLink(`/pages/pay/payment/payment?id=${this.info.id}`)
										}, 500)
									}
								})
							}
						}
					});
				}
			},
			checkIsPresalePay() {
				//判断预售是否可以支付尾款
				let now = new Date().getTime();
				var buy_str = this.info.delivery_date * 1000;
				return now > buy_str;
			},
			gotoLink(val) {
				if (this.isAllowJump) {
					this.$Router.push(val)
				}
			},
			phaseMoney(val) {
				var money = 0;
				if (val.status == '已付一期款') {
					money = val.contract_phase1;
				} else if (val.status == '已付二期款') {
					money = Number(val.contract_phase1) + Number(val.contract_phase2);
				} else if (val.status == '已付三期款') {
					money = Number(val.contract_phase1) + Number(val.contract_phase2) + Number(val.contract_phase3);
				} else if (val.status == '已支付' || val.status == '已完成') {
					money = Number(val.contract_phase1) + Number(val.contract_phase2) + Number(val.contract_phase3) + Number(val.contract_phase4);
				}
				return this.$u.priceFormat(money, 2);
			},
			cancelReturn(pro) {
				uni.showModal({
					title: "温馨提示",
					content: "是否确认取消退货?",
					confirmText: "立即确认",
					cancelText: "我再想想",
					success: (data) => {
						if (data.confirm) {
							this.$api.getOrder.cancelOrderRefund({ id: pro.id }).then(res => {
								if (res.code == 200) {
									this.$t.toast('取消成功');
									let obj = JSON.parse(JSON.stringify(this.info));
									obj.product.forEach((item, i) => {
										if (item.id == pro.id) {
											item.status = 0;
											item.order_return = '申请退货';
											item.is_return = 0;
										}
									})
									setTimeout(() => {
										this.$emit('init', obj)
									}, 500)
								}
							})
						}
					}
				});
			},
			confirmReceipt() {
				uni.showModal({
					title: "温馨提示",
					content: "是否确认收货",
					confirmText: "立即确认",
					cancelText: "我再想想",
					success: (data) => {
						if (data.confirm) {
							this.$api.getOrder.receiptOrder({ id: this.info.id }).then(res => {
								if (res.code == 200) {
									this.$t.toast('确认成功');
									setTimeout(() => {
										this.$emit('delete')
									}, 500)
								}
							})
						}
					}
				});
			},
			cancelPay() {
				if (this.info.types == 55) {
					uni.$once('deleteOrderItem', (id) => {
						console.log(id)
						if (id) {
							this.$emit('delete')
						}
					})
					this.$Router.push(`/pages/sub/errand/errand_cancel?id=${this.info.id}`)
				} else {
					uni.showModal({
						title: "温馨提示",
						content: "是否确认取消订单",
						confirmText: "立即取消",
						cancelText: "我再想想",
						success: (data) => {
							if (data.confirm) {
								this.$api.getOrder.cancelOrder({ id: this.info.id }).then(res => {
									if (res.code == 200) {
										this.$t.toast('取消成功');
										setTimeout(() => {
											this.$emit('delete')
										}, 500)
									}
								})
							}
						}
					});
				}
			},
			gotoProductDetail(item) {
				let id = item.pid;
				let mid = item.mid;
				if (this.info.types == 45) {
					return;
				}
				if (this.info.types == 25) {
					this.gotoLink(`/pages/shop/gift_card/card_list`)
				} else if (this.info.types == 77) {
					this.gotoLink(`/pages/sub/market/market_detail?id=${id}`)
				} else if (this.info.types == 15) {
					this.gotoLink(`/pages/lesson/lessoninfo/lessoninfo?id=${id}`)
				} else if (this.$Route.path == '/pages/order/storeorder/storeorder') {
					this.gotoLink(`/pages/shop/shopdetail/shopdetail?id=${id}&mid=${this.userInfo.id}`)
				} else if(id && this.$Route.path != '/pages/order/orderdetail/orderdetail') {
					this.gotoLink(`/pages/order/orderdetail/orderdetail?id=${this.info.id}`)
				} else if (id && id != 0 && mid != 0) {
					this.gotoLink(`/pages/shop/shopdetail/shopdetail?id=${id}&mid=${mid}`)
				} else if (id && id != 0) {
					this.gotoLink(`/pages/shop/shopdetail/shopdetail?id=${id}`)
				}
			},
			gotoOrderDetail() {
				// if (this.$Route.path != '/pages/order/orderdetail/orderdetail' && this.isAllowJump) {
				// 	if (this.isLifting) {
				// 		this.$Router.push(`/pages/order/orderdetail/orderdetail?id=${this.info.id}&type=lifting`)
				// 	} else if (this.isStore) {
				// 		return;
				// 	} else if (this.isSupplier) {
				// 		this.$Router.push(`/pages/order/orderdetail/orderdetail?id=${this.info.id}${this.isSupplier ? "&sub=supplier" : ""}`)
				// 	} else if (this.isRider) {
				// 		if (this.userInfo.id == this.info.rider_uid) {
				// 			this.$Router.push(`/pages/order/orderdetail/orderdetail?id=${this.info.id}${this.isRider ? "&sub=rider" : ""}`)
				// 		}
				// 	} else if (this.info.types == 39 || this.info.types == 44) {
				// 		if (this.info.is_pay == 1) {
				// 			this.$Router.push(`/pages/sub/receiving/receiving_detail?id=${this.info.id}`)
				// 		}
				// 	} else if (this.info.types == 25) {
				// 		if (this.info.is_pay == 1) {
				// 			this.$Router.push(`/pages/shop/gift_card/card_order_detail?id=${this.info.id}`)
				// 		}
				// 	} else {
				// 		this.$Router.push(`/pages/order/orderdetail/orderdetail?id=${this.info.id}`)
				// 	}
				// }else{
				// 	if(this.info.sid > 0){
				// 		this.$Router.push(`/pages/shop/supplier/supplier?id=${this.info.sid}`)
				// 	}else{
				// 		this.$t.gotoLink('/pages/home/<USER>/index')
				// 	}
				// }
				if(this.info.sid > 0){
						this.$Router.push(`/pages/shop/supplier/supplier?id=${this.info.sid}`)
				}else{
					this.$t.gotoLink('/pages/home/<USER>/index')
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	
	//#ifdef MP-WEIXIN
	$baseColor: var(--base-color);
	//#endif
	//#ifndef MP-WEIXIN
	$baseColor: var(--baseColor);
	//#endif
	.order_item {
		width: 100%;
		// height: 500rpx;
		background-color: #fff;
		margin-top: 20rpx;
		border-radius: 10rpx;
		padding: 20rpx;
		color: #222;

		.order_item_top {
			width: 100%;
			@include flex-center(row, flex-start, center);

			.order_item_top_store {
				@include flex-center(row, flex-start, center);

				>image {
					width: 45rpx;
					height: 45rpx;
					border-radius: 50%;
					margin-right: 10rpx;
				}

				>view {
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;
					margin-right: 6rpx;
				}
			}

			>text {
				font-size: 26rpx;
				font-weight: 500;
				color: #FF3B3B;
			}
		}

		.order_item_center {
			width: 100%;

			.center_pro {
				width: 100%;
				margin-top: 30rpx;
				@include flex-center(row, flex-start, flex-start);

				>image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
					margin-right: 20rpx;
				}
			}

			.center_pro_center {
				flex: 1;
				min-height: 160rpx;
				margin-right: 20rpx;
				@include flex-center(column, flex-start, flex-start);

				.center_pro_center_title {
					font-size: 28rpx;
					font-weight: normal;
					color: #333333;
					line-height: 36rpx;
					@include text_overflow(100%, 2);
					// margin-bottom: auto;
				}

				.center_pro_center_desc {
					font-size: 22rpx;
					color: #999999;
					margin: 10rpx 0 0 0;
				}
				.center_pro_center_price{
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;
					margin-top: 10rpx;
					@include flex-center(row, flex-start, center);
					.center_pro_right_price {
					
						text {
							font-weight: bold;
						}
					
						.center_pro_right_price_icon {
							font-size: 20rpx;
						}
					
						.center_pro_right_price_int {
							font-size: 32rpx;
						}
					
						.center_pro_right_price_dec {
							font-size: 24rpx;
						}
					}
				}

				.center_pro_center_desc_btn {
					font-size: 24rpx;
					color: #fff;
					border-radius: 10rpx;
					padding: 0 10rpx;
					margin-left: 10rpx;
				}

				.center_pro_center_btn {
					margin-top: 10rpx;
					@include flex-center(row, flex-start, flex-start, wrap);

					>view {
						display: inline-block;
						padding: 5rpx 8rpx;
						line-height: 1;
						border-radius: 10rpx;
						font-size: 20rpx;
						color: #FF3B3B;
						background: #fff;
						border: 2rpx solid #FF3B3B;
						margin-right: 10rpx;
						margin-bottom: 10rpx;
						@include flex-center(row, center, center);
					}
				}
			}

			.center_pro_right {
				@include flex-center(column, flex-start, flex-end);

				.center_pro_right_price {
					font-size: 28rpx;
					font-weight: bold;

					text {
						font-weight: bold;
					}

					.center_pro_right_price_icon {
						font-size: 20rpx;
					}

					.center_pro_right_price_int {
						font-size: 32rpx;
					}

					.center_pro_right_price_dec {
						font-size: 24rpx;
					}
				}

				.center_pro_right_sum {
					font-size: 24rpx;
					color: #8a8a8a;
				}
			}
		}

		.order_item_errand_address {
			margin-top: 20rpx;

			.order_item_errand_cell:nth-of-type(1) {
				border-bottom: 2rpx solid #f3f3f3;
				padding-bottom: 25rpx !important;
			}

			.order_item_errand_cell {
				width: 100%;
				padding: 25rpx 0 0 0;

				.order_item_errand_cell_top {
					@include flex-center(row, flex-start, center);

					.order_item_errand_cell_label {
						@include flex-center(row, center, center);
						width: 27rpx;
						height: 27rpx;
						font-size: 20rpx;
						color: #fff;
						border-radius: 50%;
						margin-right: 25rpx;
					}

					.order_item_errand_cell_title {
						font-size: 30rpx;
						font-weight: 500;
						color: #333333;
					}
				}

				.order_item_errand_cell_sub {
					padding-left: 50rpx;
					@include flex-center(row, flex-start, center);

					>text {
						font-size: 26rpx;
						font-weight: 500;
						color: #999999;
						line-height: 2;
					}

					>text:nth-of-type(1) {
						margin-right: 30rpx;
					}
				}
			}
		}

		.orderItem_reserve {
			margin: 20rpx 0;

			/deep/.u-collapse-item {
				background: #f9f9f9;
				border-radius: 6rpx;
				padding: 5rpx 10rpx;
			}

			/deep/.u-cell__body {
				padding-top: 10rpx;
				padding-bottom: 10rpx;
				padding-left: 10rpx;
				padding-right: 14rpx;
			}

			/deep/.u-collapse-item__content__text {
				padding-top: 5rpx;
				padding-bottom: 5rpx;
				padding-left: 14rpx;
				padding-right: 14rpx;
			}

			/deep/.u-cell__title-text {
				font-size: 28rpx;
				font-weight: bold;
			}

			.collapseItem {
				padding: 10rpx 0;
				@include flex-center(row, space-between, center);
			}

			.s1 {
				color: #9f9f9f;
				flex-shrink: 0;
				@include flex-center(row, flex-start, center);
			}

			.s2 {
				color: #222;
				text-align: right;
				@include flex-center(row, flex-end, center);
			}
		}

		.order_item_sum {
			width: 100%;
			@include flex-center(row, flex-end, center);
			margin-top: 10rpx;

			text {}

			>view {
				font-size: 26rpx;
				font-weight: 500;
				margin-left: 20rpx;
				display: table-cell;
				vertical-align: bottom;
			}

			.order_item_sum_icon {
				font-size: 24rpx;
			}

			.order_item_sum_money {
				font-size: 32rpx;
			}

			.order_item_sum_money_int {
				font-size: 32rpx;
			}

			.order_item_sum_money_dec {
				font-size: 24rpx;
			}
		}

		.order_item_btn {
			width: 100%;
			@include flex-center(row, flex-end, center);
			margin-top: 20rpx;
			margin-bottom: 5rpx;

			>view {
				font-size: 26rpx;
				border-radius: 10rpx;
				padding: 8rpx 20rpx;
				margin-left: 20rpx;
				border: 1px solid #999999;
				color: #333333;
			}

			.order_item_btn_use {
				color: #FFFFFF;
				border: none;
				// background: linear-gradient(90deg, #FE6161 0%, #FF3B3B 100%);
				background: $baseColor;
				border: 2rpx solid $baseColor;
			}

			.disabled_btn {
				opacity: 0.5;
			}
		}

		.contract_code {
			padding: 0 24rpx 40rpx;
			@include flex-center(column, center, center);

			.contract_code_tit {
				width: 100%;
				padding: 30rpx 24rpx 30rpx;
				text-align: center;
			}

			.contract_code_desc {
				width: 100%;
				text-align: right;
				font-size: 24rpx;
				color: #999999;
				padding: 20rpx 0 0;

				>text {
					color: #3c9cff;
					padding-right: 10rpx;
				}
			}
		}
	}

	.cell_part_item {
		width: 100%;
		padding: 20rpx 0rpx;
		@include flex-center(row, space-between, flex-start);

		.cell_part_item_left {
			@include flex-center(row, flex-start, center);

			>text {
				font-size: 28rpx;
				font-weight: 500;
				color: #999999;
			}
		}

		.cell_part_item_right {
			flex: 1;
			margin-left: 50rpx;
			@include flex-center(row, flex-end, center);

			>text {
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;
				text-align: right;
			}
		}
	}

	.pop_show {
		padding: 20rpx 20rpx;
		background-color: #fff;
	}
	
	.order_return{
		width: 100%;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;
		background-color: #f7f6fb;
		.order_return_header{
			width: 100%;
			height: 100rpx;
			padding: 0 40rpx;
			@include flex-center(row, space-between, center);
		}
		.order_return_body{
			width: 94%;
			margin: 0 auto;
			padding: 0 25rpx;
			padding-bottom: 20rpx;
			background-color: #fff;
			border-radius: 10rpx;
			margin-bottom: 100rpx;
		}
		.order_return_btn{
			background-color: #fff;
			padding: 20rpx 30rpx;
		}
	}
</style>