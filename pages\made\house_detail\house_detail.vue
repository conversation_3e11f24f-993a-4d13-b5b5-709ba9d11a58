<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#fff"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="全部功能" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar">
					<view slot="right" class="nav_right">
						<u-icon name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/collect_icon.png"
							size="36rpx"></u-icon>
						<u-icon name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/share_black.png"
							size="36rpx" @click="openShareMenu"></u-icon>
					</view>
				</cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<!-- 顶部图片区域 -->
				<view class="top_swiper">
					<u-swiper :list="swiperList" :keyName="swiperName" height="530rpx" radius="0"
						@change="e => currentNum = e.current" :autoplay="false" indicatorStyle="right: 20px">
						<view slot="indicator" class="indicator-num">
							<text class="indicator-num__text">{{ currentNum + 1 }}/{{ swiperList.length }}</text>
						</view>
					</u-swiper>
					<view class="swiperChange">
						<view :class="{ active: swiperStr == 'video' }" @click="changeSwiper('video')">视频</view>
						<view :class="{ active: swiperStr == 'image' }" @click="changeSwiper('image')">图片</view>
					</view>
				</view>
				<!-- 主要内容区域 -->
				<view class="main_content">
					<image class="top_image" src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/details_top.png"
						mode="scaleToFill" />
					<!-- 小区名称 -->
					<view class="community_name">人气好房·龙湖天序标准三房 靠东门业主精
						装自住</view>

					<!-- 标签区域 -->
					<view class="tags_container">
						<view class="tag tag_highlight">业主直售</view>
						<view class="tag">南北通透</view>

					</view>
					<!-- 价格信息 -->
					<view class="price_section">
						<view class="price_row">
							<view class="price_item">
								<text class="price_value">22098元/㎡</text>
								<text class="price_label">售价</text>
							</view>
							<view class="price_item">
								<text class="price_value">441-683万/套</text>
								<text class="price_label">房型</text>
							</view>
							<view class="price_item">
								<text class="price_value">3/4室</text>
								<text class="price_label">房屋面积</text>
							</view>
						</view>
					</view>
					<!-- 位置信息卡片 -->
					<view class="location_card">
						<view class="location_info" @click="goMap">
							<view class="location_text">仓山区-金山公园 龙湖兰园天序</view>
							<view class="distance_text">距离您-15.6km</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 基础信息 -->
					<view class="section">
						<view class="info_grid">
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">单价</text>
									<text class="info_value">43,805元/㎡</text>
								</view>
								<view class="info_row_btn">
									房贷计算
									<u-icon name="arrow-right" color="#999999" size="24rpx" top="1rpx"></u-icon>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">楼层</text>
									<text class="info_value">5层</text>
								</view>
								<view class="info_item">
									<text class="info_label">总层</text>
									<text class="info_value">塔板结合</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">朝向</text>
									<text class="info_value">2022年</text>
								</view>
								<view class="info_item">
									<text class="info_label">年代</text>
									<text class="info_value">70年</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">电梯</text>
									<text class="info_value">2.2</text>
								</view>
								<view class="info_item">
									<text class="info_label">类型</text>
									<text class="info_value">30%</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">用途</text>
									<text class="info_value">2.2</text>
								</view>
								<view class="info_item">
									<text class="info_label">权属</text>
									<text class="info_value">30%</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">供暖</text>
									<text class="info_value">2.2</text>
								</view>
								<view class="info_item">
									<text class="info_label">装修</text>
									<text class="info_value">30%</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">产权</text>
									<text class="info_value">2.2</text>
								</view>
								<view class="info_item">
									<text class="info_label">房本</text>
									<text class="info_value">30%</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">车位</text>
									<text class="info_value">2.2</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">小区</text>
									<text class="info_value">龙湖兰园天序(仓山区-金山公园)</text>
								</view>
								<view class="info_row_btn">
									<u-icon name="arrow-right" color="#999999" size="26rpx" top="1rpx"></u-icon>
								</view>
							</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 房屋介绍 -->
					<view class="section">
						<view class="section_title">小区概况</view>
						<view class="section_content">
							海棠公社洋房复式下叠，4室3厅4卫1厨，296.77平米，1层/5层，南北向，带装修、带电梯。采光通风很好，正对小区中心花园，也没出租过。...
						</view>
						<view class="section_btn">展开全部</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 周边配套 -->
					<view class="section">
						<view class="section_title">周边配套</view>
						<view class="section_map">
							<map :latitude="userNowPosition.latitude" :longitude="userNowPosition.longitude"
								enable-scroll="false" :markers="coverList" style="width: 100%;height:100%;" />
						</view>
						<u-tabs :list="tablist" :activeStyle="activeStyle" :inactiveStyle="inactiveStyle"
							lineColor="#006AFC" lineWidth="34rpx" lineHeight="6rpx" :itemStyle="{ height: '35px' }"
							:scrollable="false" @click="tabClick"></u-tabs>
						<view class="section_tablist">
							<view class="section_tablist_item">
								<view class="section_tablist_item_left">
									<view>台江鳌峰幼儿园</view>
									<view>台江区 | 福建省福州市台江区鳌峰路116号</view>
								</view>
								<view class="section_tablist_item_right">
									500m
								</view>
							</view>
							<view class="section_tablist_item">
								<view class="section_tablist_item_left">
									<view>台江区儿童学园</view>
									<view>台江区 | 鳌光路阳光凡尔赛A区</view>
								</view>
								<view class="section_tablist_item_right">
									650m
								</view>
							</view>
						</view>
					</view>
					<!-- 分割线 -->
					<view class="divider"></view>
					<!-- 房源推荐 -->
					<view class="section">
						<view class="section_title">房源推荐</view>
						<view class="section_house">
							<houseItem v-for="(item, index) in 3" :key="index"></houseItem>
						</view>
					</view>
				</view>

			</view>
			<view slot="bottom">
				<view class="bottom">
					<view>一键拨打业主电话</view>
				</view>

			</view>
		</z-paging>
		<shareMenu v-if="info.id" ref="shareMenu" :list="['复制链接', '生成二维码', 'app']" :appShare="appShare"
			@poster="generatePoster">
		</shareMenu>
	</view>
</template>

<script>
import houseItem from '@/components/common/house_item.vue';
import { base64ToPath, pathToBase64 } from "@/uni_modules/lime-painter/components/l-painter/utils"
import shareMenu from "@/components/common/share_menu.vue";

export default {
	components: {
		houseItem,
		shareMenu
	},
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			currentNum: 0,
			location: {
				longitude: '',
				latitude: '',
			},
			info: {
				id: 1
			},
			painterShareInfo: null,
			tablist: [{
				name: '学校',
			}, {
				name: '生活',
			}],
			activeStyle: {
				'font-weight': 'bold',
				'font-size': '30rpx',
				'color': '#333333',
			},
			inactiveStyle: {
				'font-weight': 500,
				'font-size': '30rpx',
				'color': '#646566',
			},
			swiperStr: "video", //swiper类型
			imgList: [
				{
					piclink: 'https://img1.baidu.com/it/u=1765735117,203932841&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=889'
				},
			],
			videoList: [
				{
					url: "http://t.cn/A6Fz2pHF//f.video.weibocdn.com/o0/mD08clkNlx08q5kuRGUo0104120dxm9N0E050.mp4?label=mp4_2160p60&template=3840x2160.23.0&media_id=5192247004823703&tp=8x8A3El:YTkl0eM8&us=0&ori=1&bf=4&ot=h&ps=3lckmu&uid=3ZoTIp&ab=,15568-g4,8013-g0,3601-g41&Expires=1753434574&ssig=A6GRqmGH6b&KID=unistore,video",
					title: "昨夜星辰昨夜风，画楼西畔桂堂东",
					poster: "https://img1.baidu.com/it/u=1765735117,203932841&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=889",
				},
			],
			swiperName: 'url', //swiper的key

		};
	},
	computed: {
		swiperList() {
			let arr = [];
			if (this.swiperStr == 'video') {
				arr = this.videoList;
			} else if (this.swiperStr == 'image') {
				arr = this.imgList
			}
			return arr;
		},
		coverList() {
			return [{
				id: 1, //避免下标重复
				latitude: this.userNowPosition.latitude,
				longitude: this.userNowPosition.longitude,
				iconPath: "https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/dingwei.png",
				rotate: 0,
				width: 40,
				height: 40,
				joinCluster: false,
				callout: {
					content: `仓山区 | 龙湖兰园天序`,
					fontSize: 14,
					borderRadius: 8,
					borderWidth: 0,
					bgColor: '#006AFC',
					color: '#FFFFFF',
					padding: 8,
					display: 'ALWAYS',
					textAlign: 'center',
					anchorY: -10,
					anchorX: 0,
				},
			}]
		},
		appShare() {
			let obj = {
				title: `${this.info.title}`,
				summary: `${this.info.sub_title}`,
				piclink: this.info.piclink ? this.$t.getImgUrl(this.info.piclink) : '',
			};
			return obj;
		},
	},
	onShareAppMessage(res) { //发送给朋友
		if (res.from === 'button') { // 来自页面内分享按钮
			console.log(res.target)
		}
		var shareinfo = {
			title: this.appShare.title,
			path: this.$Route.fullPath,
			imageUrl: this.appShare.piclink,
		}
		return shareinfo
	},
	onShareTimeline(res) { //分享到朋友圈
		if (res.from === 'button') { // 来自页面内分享按钮
			console.log(res.target)
		}
		var shareinfo = {
			title: this.appShare.title,
			path: this.$Route.fullPath,
			imageUrl: this.appShare.piclink,
		}
		return shareinfo
	},
	onLoad() {
		this.screenHeight = uni.getSystemInfoSync().windowHeight;
	},

	methods: {
		goMap() {
			uni.openLocation({
				latitude: Number(32),
				longitude: Number(232),
				name: '牛马',
				scale: 15
			});
		},
		changeSwiper(type) {
			this.swiperStr = type;
			this.currentNum = 0;
			if (type == 'video') {
				this.swiperName = 'url';
			} else if (type == 'image') {
				this.swiperName = 'piclink';
			}
		},
		openShareMenu() {
			if (this.$t.checkisLogin()) {
				this.$refs.shareMenu.open();
			}
		},
		generatePoster() {
			if (this.$t.checkisLogin()) {
				uni.showLoading({
					title: "生成海报中",
				});
				let share = {
					piclink: this.$t.getImgUrl(this.info.piclink),
					title: this.info.title,
					sub_title: this.info.sub_title,
					avatar: this.$t.getImgUrl(this.userInfo.avatar),
					nickname: `我是${this.userInfo.nickname || this.userInfo.username}`,
					username: `我的推广码${this.userInfo.username}`,
					shareUrl: `${this.baseConfig.shop.wx_mobile_web}${this.$Route.fullPath}`,
					qrcode: "",
				};

				let promistArr = [
					{ iden: 'piclink', piclink: this.info.piclink },
					{ iden: 'avatar', piclink: this.userInfo.avatar },
				]
				// #ifdef H5
				//跨域处理
				let promisePiclink = async (promistArr) => {
					const files = promistArr.map(async (item, i) => {
						return new Promise((resove, reject) => {
							pathToBase64(item.piclink).then(url => {
								share[item.iden] = url;
								resove(url);
							}).catch(err => {
								reject('error');
							})
						});
					});
					return await Promise.all(files);
				}
				promisePiclink(promistArr).then((res) => {
					this.painterShareInfo = share;
				}).catch(err => {
					uni.hideLoading();
					this.$t.toast('生成海报失败')
				})
				// #endif
				// #ifndef H5
				this.painterShareInfo = share;
				// #endif
			}
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},

	},
}
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.nav_right {
		@include flex-center(row, flex-end, center);
		gap: 35rpx;
	}



	.loading_list {
		width: 100%;

		.top_swiper {
			position: relative;

			.swiperChange {
				position: absolute;
				bottom: 80rpx;
				width: fit-content;
				left: 50%;
				transform: translateX(-50%);
				@include flex-center(row, center, center);
				background-color: rgba(0, 0, 0, 0.5);
				border-radius: 100px;

				>view {
					@include flex-center(row, center, center);
					width: 80rpx;
					height: 46rpx;
					border-radius: 100px;
					color: #FFFFFF;
					font-weight: 500;
					font-size: 24rpx;

					&.active {
						background: #FFFFFF;
						color: #333333;
					}
				}

			}

			.indicator-num {
				padding: 6rpx 24rpx;
				background-color: rgba(0, 0, 0, 0.5);
				border-radius: 100px;
				@include flex;
				justify-content: center;

				&__text {
					font-weight: 500;
					font-size: 24rpx;
					color: #FFFFFF;
				}
			}

			/deep/.u-swiper__indicator {
				bottom: 80rpx;
			}

			/deep/.uni-video-bar {
				height: 60rpx !important;
				bottom: 30rpx !important;
			}
		}



		// 主要内容区域
		.main_content {
			position: relative;
			top: -35rpx;
			border-radius: 18rpx 18rpx 0 0;
			overflow: hidden;

			>view {
				padding: 0 25rpx;
			}

			.top_image {
				width: 100%;
				height: 64rpx;
				padding: 0;

				>image {
					width: 100%;
					height: 100%;
				}
			}

			// 小区名称
			.community_name {
				font-size: 36rpx;
				font-weight: bold;
				color: #000000;
				line-height: 36rpx;
				padding-top: 30rpx;
			}

			// 标签区域
			.tags_container {
				@include flex-center(row, flex-start, center);
				gap: 20rpx;
				padding: 0 25rpx;
				margin-top: 20rpx;

				.tag {
					padding: 17rpx 20rpx;
					border-radius: 8rpx;
					background: #f7f7f7;
					font-size: 24rpx;
					color: #8a8a8a;

					&.tag_highlight {
						background: #f9f3ec;
						color: #b28c62;
					}
				}
			}

			// 位置信息卡片
			.location_card {
				margin: 20rpx 25rpx 0 25rpx;
				padding: 20rpx;
				background: #fafafa;
				border-radius: 20rpx;
				@include flex-center(row, space-between, center);
				box-sizing: border-box;
				background: url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/dw_bg.png') no-repeat center center;
				background-size: cover;
				background-size: 100% 100%;

				.location_info {
					flex: 0.7;
					@include flex-center(column, flex-start, flex-start);
					gap: 26rpx;

					.location_text {
						width: 100%;
						font-size: 26rpx;
						font-weight: bold;
						color: #000000;
						line-height: 26rpx;

						>view {
							@include text-overflow(100%, 1);
						}
					}

					.distance_text {
						font-size: 22rpx;
						color: #8a8a8a;
						line-height: 22rpx;
					}
				}

				.map_button {
					@include flex-center(column, center, center);
					gap: 8rpx;

					.map_text {
						font-size: 22rpx;
						color: #708ebb;
						line-height: 22rpx;
					}
				}
			}

			// 价格信息
			.price_section {
				padding: 30rpx 25rpx;

				.price_row {
					@include flex-center(row, space-between, flex-end);

					.price_item {
						flex: 1;
						@include flex-center(column, center, center);
						gap: 5rpx;

						.price_value {
							font-weight: bold;
							font-size: 34rpx;
							color: #E62222;
						}

						.price_label {
							font-weight: 500;
							font-size: 24rpx;
							color: #969696;
						}
					}
				}
			}

			// 分割线
			.divider {
				width: 100%;
				height: 15rpx;
				background: #f8f8f8;
				padding: 0;
			}

			// 章节标题和信息网格
			.section {
				padding: 30rpx 25rpx;

				.section_title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333333;
					line-height: 32rpx;
					margin-bottom: 25rpx;
				}

				.section_house {
					/deep/.house_item {
						margin-bottom: 0;
						padding: 25rpx 0;
						border-bottom: 1rpx solid #eee;
						border-radius: 0;

						&:last-child {
							border-bottom: 0;
						}
					}
				}

				.section_map {
					width: 100%;
					height: 300rpx;
					border-radius: 15rpx;
				}

				.section_tablist {
					@include flex-center(column, null, null);
					gap: 20rpx;
					margin-top: 25rpx;

					.section_tablist_item {
						@include flex-center(row, space-between, center);
						gap: 20rpx;
						width: 100%;

						.section_tablist_item_left {
							flex: 1;

							>view {
								@include text_overflow(100%, 1);
							}

							>view:nth-of-type(1) {
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
							}

							>view:nth-of-type(2) {
								font-weight: 500;
								font-size: 22rpx;
								color: #999999;
							}
						}

						.section_tablist_item_right {
							font-weight: 500;
							font-size: 28rpx;
							color: #333333;
						}
					}
				}

				.section_content {
					font-weight: 500;
					font-size: 26rpx;
					color: #999999;
					line-height: 40rpx;
				}

				.section_btn {
					font-weight: 500;
					font-size: 30rpx;
					color: #999999;
					height: 84rpx;
					background: #F4F4F4;
					border-radius: 8rpx;
					@include flex-center(row, center, center);
					margin-top: 30rpx;
				}

				.info_grid {
					display: flex;
					flex-direction: column;
					gap: 25rpx;

					.info_row {
						@include flex-center(row, space-between, center);

						&:last-child {
							margin-bottom: 0;
						}

						.info_row_btn {
							@include flex-center(row, flex-end, center);
							gap: 15rpx;
							font-weight: 500;
							font-size: 28rpx;
							color: #006AFC;
							line-height: 1;
						}

						.info_item {
							flex: 1;
							@include flex-center(row, flex-start, flex-start);
							gap: 30rpx;

							.info_label {
								width: 60rpx;
								font-size: 28rpx;
								color: #969696;
								line-height: 28rpx;
								display: flex;
								justify-content: space-between;
								text-align: justify;


							}

							.info_value {
								font-size: 28rpx;
								color: #333333;
								line-height: 28rpx;
							}
						}
					}
				}
			}
		}
	}

	.bottom {
		padding: 20rpx 25rpx;
		background-color: #fff;
		box-shadow: 0rpx -3rpx 6rpx 1rpx rgba(207, 207, 207, 0.27);
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

		>view {
			@include flex-center(row, center, center);
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			font-weight: 500;
			font-size: 30rpx;
			color: #FFFFFF;
		}
	}
}
</style>