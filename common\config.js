const CONFIG = {
	// 应用名
	name: "YK uniApp",
	// 版本号
	version: "1.1.1",
	// 开发环境配置
	development: {
		baseUrl: "http://d123.dingding1218.com/api"
	},
	// development: { baseUrl: "https://v3.vi5.cn/api" },
	// 生产环境配置
	production: {
		baseUrl: "http://d123.dingding1218.com/api"
	},
	//底部页面
	tabbarList: [
		'/pages/home/<USER>/index',
		'/pages/home/<USER>/law_firm',
		'/pages/home/<USER>/orderlist',
		'/pages/home/<USER>/userinfo',
	],
	// 是否显示请求中loading
	showLoading: true,
	// 请求loading的文字提示
	loadingText: "请求中...",
	// 请求头中身份验证字段名称
	headerTokenName: "openid",
	// 用户信息中身份验证字段名称
	userTokenName: "wechat_openid",
	// 在此时间内，请求还没回来的话，就显示加载中动画，单位ms
	loadingTime: 800,
	// 操作正常code
	successCode: 200,
	// 登录失效code
	invalidCode: 603,
	// 公众号appid
	wechatAppId: "",
	// 公众号授权方式snsapi_base或snsapi_userinfo
	wechatScope: "",
	appletAppID: "",
	appletAppSecret: "",
	// 后端返回状态码，如code,status
	codeName: "code",
	//默认头像地址
	maleDefaultAvatar: "https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/avatar_1.png",
	femaleDefaultAvatar: "https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/avatar_2.png",
	customerServiceAvatar: "https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/im/kf.png", //客服头像如果im有配置则显示配置 否则显示logo 无logo显示默认图

	//IM开关
	imOpen: true,
	autoImLogin: true, //开启则在特定页面登录IM  不开启则全局登录 		
	autoImLoginPath: [
		'/pages/chat/chat_page/chat_page',
		'/pages/chat/customer_service/customer_service',
		'/pages/live/live_pull/live_pull',
		'/pages/live/live_push/live_push',
		'/pages/live/live_obs/live_obs',
		'/pages/chat/wechat/index',
		'/pages/chat/wechat/buddy',
		'/pages/chat/wechat/usercard',
		'/pages/chat/wechat/groupcard',
		'/pages/chat/wechat/chatinfo',
		'/pages/chat/wechat/member',
		'/pages/chat/wechat/choosefriend',
		'/pages/chat/wechat/chatinfo',
		'/pages/chat/message_assistant/compose_message',
		'/pages/chat/message_assistant/new_send'
	],
	//uniPush开关
	pushOpen: false,
	gaoDeKey: "8560902de711e9d3a42c0d3982199d9e", //高德地图H5 KEY
	gaoDeAes: "c4fabbebb5f57140715975dfc25abd2a", //高德地图H5 私钥
	tencentMaPKey: "QUEBZ-7FO3W-EBQRR-RRCFM-6DUC3-ZYBHX", //腾讯地图H5 KEY

	
};
export default CONFIG;