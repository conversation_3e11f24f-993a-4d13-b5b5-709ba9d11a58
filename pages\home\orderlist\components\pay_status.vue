<template>
	<view>
		<u-popup :closeOnClickOverlay="false" :show="show" @close="show = false" round="35rpx" bgColor="#F7F7F7" zIndex="998" :overlayStyle="{zIndex:990}">
			<view class="pay_status">
				<view class="pay_status_tit" v-if="orderInfo.info">
					{{orderInfo.info}}
				</view>
				<view class="pay_status_tit" v-else-if="orderInfo.is_pay == 1">
					支付成功<text>￥</text>{{$u.priceFormat(orderInfo.money,2)}}
					<text v-if="orderInfo.is_money == 1">+{{$u.priceFormat(orderInfo.money_dk_money,2)}}
						<text style="font-size: 24rpx;">{{orderInfo.money_cn}}</text>
					</text>
				</view>
				<view class="pay_status_tit" v-else>
					支付中...
				</view>
				<view class="pay_status_oid" v-if="oidArr.length > 0">
					<view>订单号：</view>
					<view>
						<text v-if="oidArr[0]">{{oidArr[0]}}</text>
						<text v-if="oidArr && oidArr.length > 1">...</text>
					</view>
					<u-icon @click="copyKey(oidArr[0])" name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/pay/pay_copy.png" size="24rpx"></u-icon>
				</view>
				<view class="pay_status_btn" v-if="!orderInfo.info">
					<view @click="gotoDetail" :style="{background:baseColor?`linear-gradient(105deg, ${baseColor} 27%, ${$t.getOpacityColor(baseColor,0.7)} 84%)`:`linear-gradient(105deg, #fc2e38 27%, #fd4c74 84%)`}">
						<u-icon name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/pay/pay_btn1.png" size="20rpx"></u-icon>查看订单
					</view>
					<view @click="ewmShow = true;" v-if="orderInfo.kf_wx_ewm" :style="{background:baseColor?`linear-gradient(105deg, ${baseColor} 27%, ${$t.getOpacityColor(baseColor,0.7)} 84%)`:`linear-gradient(105deg, #fc2e38 27%, #fd4c74 84%)`}">
						<u-icon name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/pay/pay_btn2.png" size="20rpx"></u-icon>添加粉丝群
					</view>
				</view>

				<scroll-view :style="{height:'660rpx'}" scroll-y="true" class="scroll-Y" v-if="orderInfo.is_pay == 1">
					<view class="pay_status_cell" v-if="orderInfo.send_score > 0">
						<view>
							<u-icon name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/pay/pay_icon1.png" size="28rpx"></u-icon>
							<view>
								<text>获得{{orderInfo.send_score}}个{{orderInfo.send_score_cn}}，速速领取吧</text>
								<text>{{orderInfo.send_score_cn}}可以抵扣现金使用或兑换礼品哟</text>
							</view>
						</view>
						<view :style="{
							background:baseColor?`linear-gradient(105deg, ${baseColor} 27%, ${$t.getOpacityColor(baseColor,0.7)} 84%)`:`linear-gradient(105deg, #fc2e38 27%, #fd4c74 84%)`,
							opacity:orderInfo.is_send_score == 1? '0.6':'1'
							}" @click="sendScore">{{orderInfo.is_send_score == 1?'已领取':'去领取'}}</view>
					</view>

					<view class="pay_status_cell" v-if="orderInfo.dzp && orderInfo.dzp.id > 0">
						<view>
							<u-icon name="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/pay/pay_icon2.png" size="28rpx"></u-icon>
							<view>
								<text>获得1次抽奖大转盘机会</text>
								<text v-if="orderInfo.dzp.score > 0">最高奖励可获得{{orderInfo.dzp.score || 0}}{{orderInfo.dzp.balance_type_cn}}哟</text>
								<text v-else>可获得余额、积分、佣金、商品等更多好礼哟</text>
							</view>
						</view>
						<view :style="{
						 	background:baseColor?`linear-gradient(105deg, ${baseColor} 27%, ${$t.getOpacityColor(baseColor,0.7)} 84%)`:`linear-gradient(105deg, #fc2e38 27%, #fd4c74 84%)`,
						 	opacity:orderInfo.is_dzp == 1? '0.6':'1'
						 	}" @click="getTurnTable">{{orderInfo.is_dzp == 1 ? '已抽奖' : '去抽奖'}}</view>
					</view>

					<view class="pay_status_red" v-if="orderInfo.packet_xf && orderInfo.packet_xf.length > 0">
						<view>
							<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/pay/pay_icon3.png" mode="widthFix"></image>
							获得{{orderInfo.packet_xf.length || 1}}个消费优惠券，速速领取吧
							<text v-if="isLq == 0" @click="getXfRed('all')">一键领取</text>
						</view>
						<view>
							<view class="pay_status_reditem" v-for="(item,i) in orderInfo.packet_xf" :key="i">
								<view>
									<view class="reditem_label">{{ item.cdn_pid > 0 ? "商品券" : item.cdn_sid == 0 ? "自营券" : "商家券" }}</view>
									<image mode="widthFix" v-if="item.cdn_pid > 0 && item.p_piclink" :src="$t.getImgUrl(item.p_piclink)"></image>
									<image mode="widthFix" v-else src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/shareholder/hb.png" />
									<view class="reditem_info">
										<text>{{ item.p_title || item.title }}</text>
										<view>
											<text>￥</text>
											<text>{{ Number(item.money) }}</text>
											<text>满{{ Number(item.cdn_xfm || 0) }}可用</text>
										</view>
										<text>领取后{{item.lifetime}}日内使用有效</text>
									</view>
								</view>
								<view></view>
								<view>
									<view :style="{
										background:baseColor?`linear-gradient(105deg, ${baseColor} 27%, ${$t.getOpacityColor(baseColor,0.7)} 84%)`:`linear-gradient(105deg, #fc2e38 27%, #fd4c74 84%)`,
										opacity:orderInfo.is_lq == 1? '0.6':'1'
										}" @click="getXfRed(item)">{{item.is_lq == 1 ?'已领取':'立即领取'}}</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>

				<view class="pay_status_foot" :style="{background:baseColor?`linear-gradient(105deg, ${baseColor} 27%, ${$t.getOpacityColor(baseColor,0.7)} 84%)`:`linear-gradient(105deg, #fc2e38 27%, #fd4c74 84%)`}" @click="show = false">完成</view>
			</view>
		</u-popup>

		<turnTable ref="turnTable" @playSuccess="playSuccess"></turnTable>

		<u-popup :show="ewmShow" mode="center" @close="ewmShow = false" bgColor="transparent" :customStyle="{width:'75%'}">
			<view class="index_kf_pop">
				<view class="index_kf_top">
					<image src="https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/shareholder/wx_top.png" mode="widthFix"></image>
				</view>
				<view class="index_kf_ava">
					<image :src="$t.getImgUrl(orderInfo.kf_avatar || 'https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/user/avatar_2.png')"></image>
					<text>{{orderInfo.kf_name}}</text>
				</view>
				<!-- #ifdef H5  -->
				<view class="index_kf_con">
					<image :src="orderInfo.kf_wx_ewm"></image>
					<text>长按识别二维码</text>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5  -->
				<view class="index_kf_con">
					<view class="index_kf_num">
						<text>微信号：</text>
						<text>{{orderInfo.kf_wx_num}}</text>
						<text @click="copyKey(orderInfo.kf_wx_num)">复制</text>
					</view>
				</view>
				<!-- #endif -->
			</view>
			<view class="index_kf_close">
				<u-icon name="close-circle-fill" color="#ffffff" size="28" @click="ewmShow = false"></u-icon>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import uniCopy from "@/common/copy.js";
	import turnTable from "@/components/common/turntable.vue"
	export default {
		data() {
			return {
				oidArr: [],
				orderInfo: {
					is_pay: 0,
				},
				show: false,
				ewmShow: false
			};
		},
		options: { styleIsolation: 'shared' },
		components: {
			turnTable
		},
		mounted() {
			if (this.$Route.query.id) {
				this.getOrderInfo();
			}
		},
		computed: {
			isLq() {
				let num = 0;
				if (this.orderInfo.packet_xf) {
					for (let i in this.orderInfo.packet_xf) {
						if (this.orderInfo.packet_xf[i].is_lq == 1) {
							num = num + 1;
						}
					}
					if (num == this.orderInfo.packet_xf.length) {
						return 1;
					}
				}
				return 0;
			}
		},
		methods: {
			copyKey(val) {
				uniCopy({
					content: val,
					success: (res) => {
						this.$t.toast("复制成功");
					},
					error: (e) => {
						this.$t.toast(e || "复制失败");
					},
				});
			},
			getXfRed(item) {
				if (item == 'all' && this.isLq == 0) {
					uni.showLoading({
						title: '领取中'
					});
					this.$api.getOrder.getOrderPaySendXfCoupon({ id: this.$Route.query.id, xf_id: 0 }).then((res) => {
						if (res.code == 200) {
							setTimeout(() => {
								uni.hideLoading();
								this.$t.toast('领取成功');
								for (let i in this.orderInfo.packet_xf) {
									this.orderInfo.packet_xf[i].is_lq = 1;
								}
							}, 1000);
						} else {
							uni.hideLoading();
						}
					});
				} else if (item.is_lq != 1) {
					uni.showLoading({
						title: '领取中'
					});
					this.$api.getOrder.getOrderPaySendXfCoupon({ id: this.$Route.query.id, xf_id: item.id }).then((res) => {
						if (res.code == 200) {
							setTimeout(() => {
								uni.hideLoading();
								this.$t.toast('领取成功');
								item.is_lq = 1;
							}, 1000);
						} else {
							uni.hideLoading();
						}
					});
				}
			},
			playSuccess() {
				this.$set(this.orderInfo, 'is_dzp', 1);
			},
			getTurnTable() {
				if (this.$refs.turnTable && this.orderInfo.is_dzp != 1) {
					this.$refs.turnTable.init({ con: '支付后', oid: this.$Route.query.id })
				}
			},
			sendScore() {
				if (this.orderInfo.is_send_score != 1) {
					uni.showLoading({
						title: '领取中'
					});
					this.$api.getOrder.getOrderPaySendScore({ id: this.$Route.query.id }).then((res) => {
						if (res.code == 200) {
							setTimeout(() => {
								uni.hideLoading();
								this.$t.toast('领取成功');
								this.$set(this.orderInfo, 'is_send_score', 1);
							}, 1000);
						} else {
							uni.hideLoading();
						}
					});
				}
			},
			gotoDetail() {
				this.show = false;
				this.$Router.replace(`/pages/order/orderdetail/orderdetail?id=${this.$Route.query.id}`)
			},
			getOrderInfo() {
				this.$api.getOrder.checkPaymentStatusNew({ id: this.$Route.query.id }).then(res => {
					if (res.code == 200) {
						this.orderInfo = res.result;
						if (res.result.oid) {
							var oid = res.result.oid;
							this.oidArr = [];
							if (oid.indexOf(",") >= 0) {
								this.oidArr = oid.split(",");
							} else {
								this.oidArr.push(res.result.oid);
							}
							this.show = true;
						}
						if (this.orderInfo.is_pay == 0) {
							setTimeout(() => {
								this.getOrderStauts();
							}, 5000);
						}
					}
				})
			},
			getOrderStauts() {
				if (this.show) {
					this.$api.getOrder.checkPaymentStatusNew({ id: this.$Route.query.id }).then(res => {
						if (res.code == 200) {
							this.orderInfo = res.result;
							var oid = res.result.oid;
							this.oidArr = [];
							if (oid.indexOf(",") >= 0) {
								this.oidArr = oid.split(",");
							} else {
								this.oidArr.push(res.result.oid);
							}
							if (this.orderInfo.is_pay == 0) {
								setTimeout(() => {
									this.getOrderStauts();
								}, 5000);
							}
						}
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	.pay_status {
		width: 750rpx;
		height: 1140rpx;
		padding: 60rpx 20rpx;
		@include flex-center(column, center, center);

		.pay_status_tit {
			font-size: 48rpx;
			font-weight: bold;
			color: #343434;

			>text {
				font-size: 36rpx;
				padding-left: 10rpx;
			}
		}

		.pay_status_oid {
			font-size: 28rpx;
			font-weight: 500;
			color: #666666;
			padding-top: 25rpx;
			@include flex-center(row, center, flex-start);

			/deep/.u-icon {
				margin-left: 10rpx;
				margin-top: 8rpx;
			}
		}

		.pay_status_btn {
			@include flex-center(row, center, center);
			font-size: 28rpx;
			color: #FFFFFF;
			margin: 40rpx 0 20rpx;

			/deep/.u-icon {
				margin-right: 10rpx;
			}

			>view:nth-of-type(1) {
				width: 238rpx;
				height: 66rpx;
				background: linear-gradient(90deg, #FFC630 0%, #FFA63A 100%);
				border-radius: 10rpx;
				@include flex-center(row, center, center);
				margin: 0 27rpx;
			}

			>view:nth-of-type(2) {
				width: 238rpx;
				height: 66rpx;
				background: linear-gradient(90deg, #FF9D64 0%, #F55635 100%);
				border-radius: 10rpx;
				@include flex-center(row, center, center);
				margin: 0 27rpx;
			}
		}

		.pay_status_cell {
			width: 710rpx;
			min-height: 127rpx;
			background: #FFFFFF;
			border-radius: 12rpx;
			margin: 20rpx auto 0;
			padding: 20rpx;
			@include flex-center(row, center, center);

			>view:nth-of-type(1) {
				flex: 1;
				@include flex-center(row, flex-start, flex-start);

				/deep/.u-icon {
					margin-top: 8rpx;
					margin-right: 16rpx;
				}

				>view {
					@include flex-center(column, center, flex-start);

					>text:nth-of-type(1) {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
					}

					>text:nth-of-type(2) {
						font-size: 22rpx;
						font-weight: 500;
						color: #999999;
						padding-top: 6rpx;
					}
				}
			}

			>view:nth-of-type(2) {
				width: 112rpx;
				height: 41rpx;
				background: linear-gradient(85deg, #FFC830 0%, #FFA33A 100%);
				border-radius: 100rpx;
				font-size: 24rpx;
				font-weight: 500;
				color: #FFFFFF;
				margin-left: 20rpx;
				@include flex-center(row, center, center);
			}
		}

		.pay_status_red {
			width: 710rpx;
			min-height: 127rpx;
			background: #FFFFFF;
			border-radius: 12rpx;
			margin: 20rpx auto 0;
			padding: 20rpx;

			// @include flex-center(row, center, center);
			>view:nth-of-type(1) {
				font-size: 28rpx;
				font-weight: bold;
				color: #333333;
				@include flex-center(row, flex-start, center);
				padding-bottom: 20rpx;
				border-bottom: 1rpx solid #f4f4f4;

				image {
					width: 28rpx;
					margin-right: 16rpx;
				}

				>text {
					margin-left: auto;
					width: 140rpx;
					height: 41rpx;
					background: linear-gradient(85deg, #FFC830 0%, #FFA33A 100%);
					border-radius: 100rpx;
					font-size: 24rpx;
					font-weight: 500;
					color: #FFFFFF;
					@include flex-center(row, center, center);
				}
			}

			>view:nth-of-type(2) {
				.pay_status_reditem {
					width: 100%;
					height: 170rpx;
					border-radius: 10rpx;
					margin: 20rpx auto;
					@include flex-center(row, space-between, center);

					>view:nth-of-type(1) {
						flex: 1;
						height: 100%;
						box-shadow: 0 0 6rpx 0 rgba(0, 0, 0, 0.10);
						border-radius: 10rpx;
						position: relative;
						padding: 15rpx;
						@include flex-center(row, space-between, center);

						.reditem_label {
							width: 100rpx;
							background: #FE3836;
							border-radius: 30rpx 100rpx 100rpx 20rpx;
							background: url("https://inexweb.oss-cn-shenzhen.aliyuncs.com/uni/shareholder/coupon_label.png") no-repeat;
							background-size: 100% 100%;
							background-position: center;
							font-size: 24rpx;
							text-align: center;
							padding-bottom: 6rpx;
							color: #ffffff;
							position: absolute;
							left: -10rpx;
							top: 9rpx;
						}

						>image {
							width: 98rpx;
							border-radius: 8rpx;
							margin-top: 40rpx;
							margin-right: 20rpx;
						}

						.reditem_info {
							flex: 1;
							height: 150rpx;
							@include flex-center(column, space-between, flex-start);

							>text:nth-of-type(1) {
								font-size: 28rpx;
								font-weight: bold;
								color: #333333;
								line-height: 1.5;
								@include line_overflow(340rpx);
								padding-bottom: 20rpx;
							}

							>view:nth-of-type(1) {
								color: #F3383A;
								font-size: 20rpx;

								>text:nth-of-type(2) {
									font-size: 34rpx;
									font-weight: bold;
									padding-right: 10rpx;
								}
							}

							>text:nth-of-type(2) {
								font-size: 20rpx;
								color: #999999;
								padding-bottom: 10rpx;
							}
						}
					}

					>view:nth-of-type(2) {
						width: 0rpx;
						height: 150rpx;
						opacity: 0.20;
						border-right: 1rpx dashed #7f7f7f;
					}

					>view:nth-of-type(3) {
						width: 180rpx;
						height: 100%;
						background: #fafafa;
						box-shadow: 0 0 6rpx 0 rgba(0, 0, 0, 0.10);
						border-radius: 10rpx;
						@include flex-center(row, center, center);

						>view {
							width: 130rpx;
							height: 40rpx;
							border-radius: 100rpx;
							font-size: 24rpx;
							font-weight: 500;
							color: #FFFFFF;
							@include flex-center(row, center, center);
						}
					}
				}
			}
		}

		.pay_status_foot {
			width: 710rpx;
			height: 86rpx;
			background: linear-gradient(90deg, #FFC730 0%, #FFA439 100%);
			border-radius: 15rpx;
			margin-top: auto;
			font-size: 32rpx;
			font-weight: bold;
			color: #FFFFFF;
			@include flex-center(row, center, center);
		}
	}

	.index_kf_pop {
		background: #ffffff;
		border-radius: 20rpx;

		.index_kf_top {
			width: 100%;
			border-radius: 20rpx 20rpx 0 0;

			image {
				width: 100%;
			}
		}

		.index_kf_ava {
			width: 100%;
			margin-top: -90rpx;
			@include flex-center(column, center, center);

			image {
				width: 150rpx;
				height: 150rpx;
				border-radius: 50%;
				border: 4rpx solid #fff;
			}

			text {
				font-size: 32rpx;
				color: #333333;
				font-weight: 500;
				margin: 20rpx 0;
			}
		}

		.index_kf_con {
			width: 100%;
			padding: 0 0 20px;
			@include flex-center(column, center, center);

			>image {
				width: 280rpx;
				height: 280rpx;
				padding: 4rpx;
				border: 2rpx solid #4a70ef;
				border-radius: 10rpx;
			}

			>text {
				line-height: 44rpx;
				color: #fff;
				font-size: 24rpx;
				padding: 0px 10rpx;
				background-color: #4a70ef;
				margin-top: 40rpx;
				border-radius: 10rpx;
			}

			.index_kf_num {
				margin-top: 20rpx;
				@include flex-center(row, center, center);

				>text:nth-of-type(1) {
					font-size: 28rpx;
				}

				>text:nth-of-type(2) {
					min-width: 100rpx;
					font-weight: normal;
					font-size: 28rpx;
					border-radius: 10rpx;
					padding: 10rpx 20rpx 14rpx;
					color: #8b8b8b;
					background-color: #e5e5e5;
				}

				>text:nth-of-type(3) {
					display: block;
					color: #fff;
					border-radius: 10rpx;
					font-size: 26rpx;
					padding: 5rpx 14rpx;
					background-color: #4a70ef;
					margin-left: 16rpx;
				}
			}
		}
	}

	.index_kf_close {
		width: 100%;
		padding-top: 30rpx;
		@include flex-center(row, center, center);
	}
</style>